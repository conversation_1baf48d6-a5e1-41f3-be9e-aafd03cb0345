// Node.js 图标生成脚本
const fs = require('fs');
const path = require('path');

// 创建SVG图标的函数
function createSVGIcon(iconType, isActive = false) {
  const color = isActive ? '#1E3A8A' : '#666666';
  const size = 64;
  
  let iconPath = '';
  
  switch (iconType) {
    case 'home':
      iconPath = `
        <path d="M32 12L16 26v22h12V36h8v12h12V26L32 12z" fill="${color}"/>
        <path d="M32 8l-3 2.5L8 28v24h16V40h16v12h16V28L35 10.5L32 8z" stroke="${color}" stroke-width="1" fill="none"/>
      `;
      break;
    case 'products':
      iconPath = `
        <rect x="16" y="20" width="32" height="24" rx="2" fill="${color}"/>
        <rect x="20" y="16" width="24" height="4" rx="1" fill="${color}"/>
        <rect x="24" y="28" width="16" height="2" fill="white"/>
        <rect x="24" y="32" width="12" height="2" fill="white"/>
      `;
      break;
    case 'calculator':
      iconPath = `
        <rect x="16" y="12" width="32" height="40" rx="4" fill="${color}"/>
        <rect x="20" y="16" width="24" height="8" rx="2" fill="white"/>
        <circle cx="24" cy="32" r="2" fill="white"/>
        <circle cx="32" cy="32" r="2" fill="white"/>
        <circle cx="40" cy="32" r="2" fill="white"/>
        <circle cx="24" cy="40" r="2" fill="white"/>
        <circle cx="32" cy="40" r="2" fill="white"/>
        <circle cx="40" cy="40" r="2" fill="white"/>
        <circle cx="24" cy="48" r="2" fill="white"/>
        <circle cx="32" cy="48" r="2" fill="white"/>
        <circle cx="40" cy="48" r="2" fill="white"/>
      `;
      break;
    case 'profile':
      iconPath = `
        <circle cx="32" cy="22" r="8" fill="${color}"/>
        <path d="M20 52c0-8 5.4-12 12-12s12 4 12 12" stroke="${color}" stroke-width="4" fill="none"/>
      `;
      break;
    default:
      iconPath = `<circle cx="32" cy="32" r="16" fill="${color}"/>`;
  }
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  ${iconPath}
</svg>`;
}

// 创建目录
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 生成所有图标
function generateIcons() {
  const iconsDir = path.join(__dirname, 'images', 'tab');
  ensureDir(iconsDir);
  
  const iconTypes = ['home', 'products', 'calculator', 'profile'];
  
  iconTypes.forEach(iconType => {
    // 普通状态图标
    const normalIcon = createSVGIcon(iconType, false);
    fs.writeFileSync(path.join(iconsDir, `${iconType}.svg`), normalIcon);
    
    // 激活状态图标
    const activeIcon = createSVGIcon(iconType, true);
    fs.writeFileSync(path.join(iconsDir, `${iconType}-active.svg`), activeIcon);
  });
  
  console.log('图标生成完成！');
  console.log('生成的文件：');
  iconTypes.forEach(iconType => {
    console.log(`- images/tab/${iconType}.svg`);
    console.log(`- images/tab/${iconType}-active.svg`);
  });
}

// 如果直接运行此脚本
if (require.main === module) {
  generateIcons();
}

module.exports = { generateIcons, createSVGIcon };
