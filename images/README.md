# 图片资源目录

## 目录结构

```
images/
├── tab/                    # 底部导航图标
│   ├── home.png           # 首页图标
│   ├── home-active.png    # 首页激活图标
│   ├── products.png       # 产品图标
│   ├── products-active.png # 产品激活图标
│   ├── calculator.png     # 计算器图标
│   ├── calculator-active.png # 计算器激活图标
│   ├── profile.png        # 个人中心图标
│   └── profile-active.png # 个人中心激活图标
├── icons/                 # 功能图标
│   ├── order.png          # 下单图标
│   ├── calculator.png     # 计算器图标
│   ├── sample.png         # 样品图标
│   ├── contact.png        # 客服图标
│   ├── box.png            # 包装盒图标
│   ├── bag.png            # 手提袋图标
│   ├── brochure.png       # 宣传册图标
│   ├── card.png           # 名片图标
│   ├── sticker.png        # 标签图标
│   └── other.png          # 其他图标
├── banners/               # 轮播图
│   ├── banner1.jpg        # 轮播图1
│   ├── banner2.jpg        # 轮播图2
│   └── banner3.jpg        # 轮播图3
├── categories/            # 产品分类图片
│   ├── box.jpg            # 包装盒
│   ├── bag.jpg            # 手提袋
│   ├── brochure.jpg       # 宣传册
│   ├── card.jpg           # 名片
│   ├── sticker.jpg        # 标签贴纸
│   └── other.jpg          # 其他
├── products/              # 产品图片
│   └── (产品图片文件)
├── empty/                 # 空状态图片
│   ├── products.png       # 产品为空
│   ├── orders.png         # 订单为空
│   └── favorites.png      # 收藏为空
└── share/                 # 分享图片
    ├── share.jpg          # 默认分享图
    ├── share-products.jpg # 产品分享图
    └── share-calculator.jpg # 计算器分享图
```

## 图片规范

### 尺寸要求
- **底部导航图标**：64x64px (2倍图)
- **功能图标**：64x64px (2倍图)
- **轮播图**：750x400px (2倍图)
- **产品分类图**：400x300px (2倍图)
- **产品图片**：600x600px (2倍图)
- **空状态图**：400x400px (2倍图)
- **分享图**：500x400px

### 格式要求
- **图标**：PNG格式，支持透明背景
- **照片**：JPG格式，压缩质量80-90%
- **插画**：PNG格式，保持清晰度

### 命名规范
- 使用小写字母和连字符
- 描述性命名，便于理解
- 激活状态添加 `-active` 后缀
- 不同尺寸添加 `@2x`、`@3x` 后缀

## 注意事项

1. 所有图片需要提供2倍图以适配高分辨率屏幕
2. 图标需要保持统一的设计风格
3. 产品图片需要高质量，展示产品细节
4. 轮播图需要突出重点信息，文字清晰可读
5. 空状态图片要友好，符合品牌调性

## 图片优化

- 使用工具压缩图片大小
- 保持图片质量和文件大小的平衡
- 考虑使用 WebP 格式（如果支持）
- 懒加载非关键图片
