# 小森包装小程序图标制作指南

## 🎯 图标需求清单

### 底部导航图标 (64x64px)
- `images/tab/home.png` - 首页图标（未激活状态）
- `images/tab/home-active.png` - 首页图标（激活状态）
- `images/tab/products.png` - 产品图标（未激活状态）
- `images/tab/products-active.png` - 产品图标（激活状态）
- `images/tab/calculator.png` - 计算器图标（未激活状态）
- `images/tab/calculator-active.png` - 计算器图标（激活状态）
- `images/tab/profile.png` - 个人中心图标（未激活状态）
- `images/tab/profile-active.png` - 个人中心图标（激活状态）

### 功能图标 (64x64px)
- `images/icons/order.png` - 下单图标
- `images/icons/sample.png` - 样品图标
- `images/icons/contact.png` - 客服图标
- `images/icons/box.png` - 包装盒图标
- `images/icons/bag.png` - 手提袋图标
- `images/icons/brochure.png` - 宣传册图标
- `images/icons/card.png` - 名片图标
- `images/icons/sticker.png` - 标签图标
- `images/icons/other.png` - 其他图标

## 🎨 设计规范

### 颜色规范
- **未激活状态**：#666666 (灰色)
- **激活状态**：#1E3A8A (深蓝色)
- **背景色**：透明或白色

### 尺寸规范
- **图标尺寸**：64x64px (2倍图)
- **有效绘制区域**：56x56px (留4px边距)
- **线条粗细**：2-3px
- **圆角半径**：2-4px

### 风格要求
- **简洁明了**：图标含义清晰，一目了然
- **风格统一**：所有图标保持一致的设计风格
- **适配性好**：在小尺寸下依然清晰可辨
- **品牌一致**：符合小森包装的品牌调性

## 🛠 制作方法

### 方法一：使用设计软件
1. **推荐软件**：Sketch、Figma、Adobe Illustrator、Photoshop
2. **创建画布**：64x64px，分辨率144dpi
3. **设计图标**：在56x56px区域内设计
4. **导出设置**：PNG格式，透明背景

### 方法二：使用在线工具
1. 打开项目根目录的 `create-icons.html` 文件
2. 在浏览器中打开该文件
3. 点击"下载所有图标"按钮
4. 将下载的图标文件放到对应目录

### 方法三：使用图标库
1. **推荐网站**：
   - Iconfont (阿里巴巴矢量图标库)
   - Feather Icons
   - Heroicons
   - Material Design Icons
2. **搜索关键词**：home, product, calculator, user, box, bag等
3. **下载并调整**：下载SVG格式，调整颜色和尺寸

## 📁 文件组织

```
images/
├── tab/                    # 底部导航图标
│   ├── home.png
│   ├── home-active.png
│   ├── products.png
│   ├── products-active.png
│   ├── calculator.png
│   ├── calculator-active.png
│   ├── profile.png
│   └── profile-active.png
└── icons/                  # 功能图标
    ├── order.png
    ├── sample.png
    ├── contact.png
    ├── box.png
    ├── bag.png
    ├── brochure.png
    ├── card.png
    ├── sticker.png
    └── other.png
```

## 🔧 添加图标到项目

1. **制作完成图标**后，将文件放到对应目录
2. **更新 app.json**，添加图标路径：

```json
"tabBar": {
  "color": "#666666",
  "selectedColor": "#1E3A8A",
  "backgroundColor": "#ffffff",
  "borderStyle": "black",
  "list": [
    {
      "pagePath": "pages/index/index",
      "text": "首页",
      "iconPath": "images/tab/home.png",
      "selectedIconPath": "images/tab/home-active.png"
    },
    {
      "pagePath": "pages/products/products",
      "text": "产品",
      "iconPath": "images/tab/products.png",
      "selectedIconPath": "images/tab/products-active.png"
    },
    {
      "pagePath": "pages/calculator/calculator",
      "text": "报价",
      "iconPath": "images/tab/calculator.png",
      "selectedIconPath": "images/tab/calculator-active.png"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "我的",
      "iconPath": "images/tab/profile.png",
      "selectedIconPath": "images/tab/profile-active.png"
    }
  ]
}
```

## ✅ 检查清单

- [ ] 所有图标尺寸正确 (64x64px)
- [ ] 图标风格统一
- [ ] 颜色符合规范
- [ ] 文件命名正确
- [ ] 文件格式为PNG
- [ ] 背景透明
- [ ] 在小程序中测试显示效果

## 💡 小贴士

1. **批量处理**：如果有多个图标需要调整，可以使用批处理工具
2. **预览效果**：在微信开发者工具中实时预览图标效果
3. **备份原文件**：保留原始设计文件，便于后续修改
4. **版本管理**：为图标文件建立版本管理，记录修改历史

完成图标制作后，您的小程序就能正常显示底部导航图标了！
