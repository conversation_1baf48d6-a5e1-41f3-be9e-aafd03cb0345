<!DOCTYPE html>
<html>
<head>
    <title>生成小程序图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .icon-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .icon-item { text-align: center; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #fafafa; }
        canvas { border: 1px solid #ccc; margin: 10px 0; background: white; }
        button { padding: 12px 24px; margin: 10px; background: #1E3A8A; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; }
        button:hover { background: #1E40AF; }
        .download-all { background: #F97316; }
        .download-all:hover { background: #EA580C; }
        h1 { color: #1E3A8A; text-align: center; margin-bottom: 30px; }
        h3 { color: #374151; margin-bottom: 15px; }
        .instructions { background: #EFF6FF; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #1E3A8A; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 小森包装小程序图标生成器</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <p>1. 点击下方的"生成并下载所有图标"按钮</p>
            <p>2. 图标将自动下载到您的下载文件夹</p>
            <p>3. 将下载的PNG文件复制到项目的 <code>images/tab/</code> 目录</p>
            <p>4. 更新 app.json 文件中的图标路径配置</p>
        </div>
        
        <div class="icon-grid" id="iconGrid"></div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button class="download-all" onclick="downloadAllIcons()">🚀 生成并下载所有图标</button>
        </div>
    </div>

    <script>
        // 图标配置
        const iconConfigs = [
            { name: 'home', title: '首页', symbol: '🏠' },
            { name: 'products', title: '产品', symbol: '📦' },
            { name: 'calculator', title: '报价', symbol: '🧮' },
            { name: 'profile', title: '我的', symbol: '👤' }
        ];

        // 创建图标
        function createIcon(config, isActive = false) {
            const canvas = document.createElement('canvas');
            canvas.width = 64;
            canvas.height = 64;
            const ctx = canvas.getContext('2d');
            
            // 设置颜色
            const bgColor = isActive ? '#E3F2FD' : '#F5F5F5';
            const iconColor = isActive ? '#1E3A8A' : '#666666';
            const textColor = isActive ? '#1E3A8A' : '#666666';
            
            // 绘制背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, 64, 64);
            
            // 绘制图标
            ctx.fillStyle = iconColor;
            ctx.strokeStyle = iconColor;
            ctx.lineWidth = 2;
            
            switch (config.name) {
                case 'home':
                    // 房子图标
                    ctx.beginPath();
                    ctx.moveTo(32, 16);
                    ctx.lineTo(16, 28);
                    ctx.lineTo(20, 28);
                    ctx.lineTo(20, 44);
                    ctx.lineTo(44, 44);
                    ctx.lineTo(44, 28);
                    ctx.lineTo(48, 28);
                    ctx.closePath();
                    ctx.fill();
                    // 门
                    ctx.fillStyle = bgColor;
                    ctx.fillRect(28, 36, 8, 8);
                    break;
                    
                case 'products':
                    // 盒子图标
                    ctx.fillRect(18, 26, 28, 20);
                    ctx.strokeRect(18, 26, 28, 20);
                    // 盒盖
                    ctx.fillRect(16, 22, 32, 6);
                    ctx.strokeRect(16, 22, 32, 6);
                    break;
                    
                case 'calculator':
                    // 计算器图标
                    ctx.fillRect(18, 16, 28, 32);
                    ctx.strokeRect(18, 16, 28, 32);
                    // 屏幕
                    ctx.fillStyle = bgColor;
                    ctx.fillRect(22, 20, 20, 8);
                    // 按键
                    ctx.fillStyle = bgColor;
                    for (let i = 0; i < 3; i++) {
                        for (let j = 0; j < 3; j++) {
                            ctx.fillRect(24 + j * 6, 32 + i * 4, 4, 3);
                        }
                    }
                    break;
                    
                case 'profile':
                    // 人物图标
                    ctx.beginPath();
                    ctx.arc(32, 26, 8, 0, 2 * Math.PI);
                    ctx.fill();
                    // 身体
                    ctx.fillRect(22, 36, 20, 12);
                    ctx.beginPath();
                    ctx.arc(32, 36, 10, 0, Math.PI);
                    ctx.fill();
                    break;
            }
            
            // 添加文字
            ctx.fillStyle = textColor;
            ctx.font = 'bold 9px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(config.title, 32, 56);
            
            return canvas;
        }

        // 下载图标
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 下载所有图标
        function downloadAllIcons() {
            iconConfigs.forEach(config => {
                // 普通状态
                const normalCanvas = createIcon(config, false);
                downloadIcon(normalCanvas, `${config.name}.png`);
                
                // 激活状态
                const activeCanvas = createIcon(config, true);
                downloadIcon(activeCanvas, `${config.name}-active.png`);
            });
            
            alert('✅ 所有图标已生成并开始下载！\n\n请将下载的PNG文件复制到项目的 images/tab/ 目录中。');
        }

        // 初始化页面
        function initPage() {
            const grid = document.getElementById('iconGrid');
            
            iconConfigs.forEach(config => {
                const item = document.createElement('div');
                item.className = 'icon-item';
                
                const normalCanvas = createIcon(config, false);
                const activeCanvas = createIcon(config, true);
                
                item.innerHTML = `
                    <h3>${config.symbol} ${config.title}</h3>
                    <div>
                        <div>普通状态</div>
                        ${normalCanvas.outerHTML}
                        <div>激活状态</div>
                        ${activeCanvas.outerHTML}
                    </div>
                    <button onclick="downloadSingle('${config.name}')">下载这组图标</button>
                `;
                
                grid.appendChild(item);
            });
        }

        // 下载单个图标组
        function downloadSingle(iconName) {
            const config = iconConfigs.find(c => c.name === iconName);
            if (config) {
                const normalCanvas = createIcon(config, false);
                downloadIcon(normalCanvas, `${config.name}.png`);
                
                const activeCanvas = createIcon(config, true);
                downloadIcon(activeCanvas, `${config.name}-active.png`);
            }
        }

        // 页面加载完成后初始化
        window.onload = initPage;
    </script>
</body>
</html>
