<!DOCTYPE html>
<html>
<head>
    <title>创建小程序图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 10px; background: #1E3A8A; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .download-section { margin: 20px 0; padding: 20px; background: #f5f5f5; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>小森包装小程序图标生成器</h1>
    <p>这个工具可以帮助您快速生成小程序所需的基础图标</p>
    
    <div class="icon-container">
        <h3>底部导航图标 (64x64px)</h3>
        <canvas id="homeIcon" width="64" height="64"></canvas>
        <canvas id="homeActiveIcon" width="64" height="64"></canvas>
        <canvas id="productsIcon" width="64" height="64"></canvas>
        <canvas id="productsActiveIcon" width="64" height="64"></canvas>
        <canvas id="calculatorIcon" width="64" height="64"></canvas>
        <canvas id="calculatorActiveIcon" width="64" height="64"></canvas>
        <canvas id="profileIcon" width="64" height="64"></canvas>
        <canvas id="profileActiveIcon" width="64" height="64"></canvas>
    </div>
    
    <div class="download-section">
        <h3>下载图标</h3>
        <button onclick="downloadAllIcons()">下载所有图标</button>
        <p>点击按钮后，图标将自动下载到您的下载文件夹</p>
    </div>

    <script>
        // 图标配置
        const iconConfigs = [
            { id: 'homeIcon', name: 'home.png', text: '首页', color: '#666666', bgColor: '#f0f0f0' },
            { id: 'homeActiveIcon', name: 'home-active.png', text: '首页', color: '#1E3A8A', bgColor: '#e3f2fd' },
            { id: 'productsIcon', name: 'products.png', text: '产品', color: '#666666', bgColor: '#f0f0f0' },
            { id: 'productsActiveIcon', name: 'products-active.png', text: '产品', color: '#1E3A8A', bgColor: '#e3f2fd' },
            { id: 'calculatorIcon', name: 'calculator.png', text: '报价', color: '#666666', bgColor: '#f0f0f0' },
            { id: 'calculatorActiveIcon', name: 'calculator-active.png', text: '报价', color: '#1E3A8A', bgColor: '#e3f2fd' },
            { id: 'profileIcon', name: 'profile.png', text: '我的', color: '#666666', bgColor: '#f0f0f0' },
            { id: 'profileActiveIcon', name: 'profile-active.png', text: '我的', color: '#1E3A8A', bgColor: '#e3f2fd' }
        ];

        // 创建图标
        function createIcon(config) {
            const canvas = document.getElementById(config.id);
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 64, 64);
            
            // 绘制背景
            ctx.fillStyle = config.bgColor;
            ctx.fillRect(0, 0, 64, 64);
            
            // 绘制图标形状（简单的几何形状）
            ctx.fillStyle = config.color;
            ctx.strokeStyle = config.color;
            ctx.lineWidth = 2;
            
            if (config.text === '首页') {
                // 房子形状
                ctx.beginPath();
                ctx.moveTo(32, 15);
                ctx.lineTo(15, 30);
                ctx.lineTo(20, 30);
                ctx.lineTo(20, 45);
                ctx.lineTo(44, 45);
                ctx.lineTo(44, 30);
                ctx.lineTo(49, 30);
                ctx.closePath();
                ctx.fill();
            } else if (config.text === '产品') {
                // 盒子形状
                ctx.fillRect(20, 25, 24, 20);
                ctx.strokeRect(20, 25, 24, 20);
                ctx.fillRect(18, 20, 28, 5);
            } else if (config.text === '报价') {
                // 计算器形状
                ctx.fillRect(18, 18, 28, 28);
                ctx.fillStyle = config.bgColor;
                for (let i = 0; i < 3; i++) {
                    for (let j = 0; j < 3; j++) {
                        ctx.fillRect(22 + j * 6, 22 + i * 6, 4, 4);
                    }
                }
            } else if (config.text === '我的') {
                // 人形图标
                ctx.beginPath();
                ctx.arc(32, 25, 8, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillRect(24, 35, 16, 12);
            }
            
            // 添加文字标签
            ctx.fillStyle = config.color;
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(config.text, 32, 58);
        }

        // 下载图标
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // 下载所有图标
        function downloadAllIcons() {
            iconConfigs.forEach(config => {
                const canvas = document.getElementById(config.id);
                downloadIcon(canvas, config.name);
            });
        }

        // 初始化所有图标
        window.onload = function() {
            iconConfigs.forEach(createIcon);
        };
    </script>
</body>
</html>
