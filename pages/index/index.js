// 首页
const app = getApp();
const { get } = require("../../utils/request");

Page({
  data: {
    // 轮播图数据
    banners: [
      {
        id: 1,
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzUwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMUUzQThBIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzNiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7kuJPkuJrlMIXoo4XlrprkuLogPC90ZXh0Pjwvc3ZnPg==",
        title: "专业包装贸易",
        link: "/pages/products/products",
      },
      {
        id: 2,
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzUwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjRjk3MzE2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzNiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7kvJjotKjljbDliLfmnI3liqE8L3RleHQ+PC9zdmc+",
        title: "优质产品供应",
        link: "/pages/calculator/calculator",
      },
      {
        id: 3,
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzUwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMTBCOTgxIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzNiIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lhY3otLnmoLflk4HnlLPor7c8L3RleHQ+PC9zdmc+",
        title: "免费样品申请",
        link: "/pages/sample/sample",
      },
    ],

    // 快速入口
    quickEntries: [
      {
        id: 1,
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9IiNGOTczMTYiIHJ4PSI4Ii8+PHBhdGggZD0iTTIwIDI0aDI0djE2SDIweiIgZmlsbD0id2hpdGUiLz48L3N2Zz4=",
        title: "快速下单",
        subtitle: "一键下单",
        link: "/pages/order/order",
      },
      {
        id: 2,
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9IiMzQjgyRjYiIHJ4PSI4Ii8+PHJlY3QgeD0iMTgiIHk9IjE4IiB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIGZpbGw9IndoaXRlIiByeD0iNCIvPjwvc3ZnPg==",
        title: "价格计算",
        subtitle: "实时报价",
        link: "/pages/calculator/calculator",
      },
      {
        id: 3,
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9IiMxMEI5ODEiIHJ4PSI4Ii8+PHJlY3QgeD0iMjAiIHk9IjIwIiB3aWR0aD0iMjQiIGhlaWdodD0iMjAiIGZpbGw9IndoaXRlIiByeD0iMiIvPjwvc3ZnPg==",
        title: "样品申请",
        subtitle: "免费寄送",
        link: "/pages/sample/sample",
      },
      {
        id: 4,
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIGZpbGw9IiM4QjVDRjYiIHJ4PSI4Ii8+PGNpcmNsZSBjeD0iMzIiIGN5PSIyNiIgcj0iOCIgZmlsbD0id2hpdGUiLz48cGF0aCBkPSJNMjIgNDJjMC04IDQuNS0xMiAxMC0xMnMxMCA0IDEwIDEyIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjQiIGZpbGw9Im5vbmUiLz48L3N2Zz4=",
        title: "联系客服",
        subtitle: "在线咨询",
        link: "/pages/contact/contact",
      },
    ],

    // 产品分类（首页展示）
    categories: [
      {
        id: 1,
        name: "特产箱",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCBmaWxsPSIjRkY5NTAwIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIi8+PHRleHQgeD0iMjAwIiB5PSIyMDAiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7kvY3ku6Plv7Xpg6gmPC90ZXh0Pjwvc3ZnPi",
      },
      {
        id: 2,
        name: "海鲜礼盒",
        image: "/images/products/haixian/haixian1.jpg",
      },
      {
        id: 3,
        name: "月饼礼盒",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCBmaWxsPSIjNjM2NkY3IiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIi8+PHRleHQgeD0iMjAwIiB5PSIyMDAiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lpKfmiJblj6/miqU8L3RleHQ+PC9zdmc+",
      },
      {
        id: 4,
        name: "坚果礼盒",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCBmaWxsPSIjRkY2Mzk0IiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIi8+PHRleHQgeD0iMjAwIiB5PSIyMDAiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ljYHmiqLotK0vPC90ZXh0Pjwvc3ZnPi",
      },
      {
        id: 5,
        name: "卡盒",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCBmaWxsPSIjMTBCOTgxIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIi8+PHRleHQgeD0iMjAwIiB5PSIyMDAiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ljbPpg6g8L3RleHQ+PC9zdmc+",
      },
      {
        id: 6,
        name: "手提袋",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCBmaWxsPSIjOEI1Q0Y2IiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIi8+PHRleHQgeD0iMjAwIiB5PSIyMDAiIGZpbGw9IndoaXRlIiBmb250LXNpemU9IjI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lpKflubQ8L3RleHQ+PC9zdmc+",
      },
      {
        id: 7,
        name: "纸箱",
        image: "/images/products/box/box2.jpg",
      },
    ],

    // 热门产品
    hotProducts: [
      {
        "id": 1,
        "name": "海鲜1",
        "price": "199.00",
        "image": "/images/products/haixian/haixian1.jpg"
      },
      {
        "id": 2,
        "name": "海鲜2",
        "price": "299.00",
        "image": "/images/products/haixian/haixian2.jpg"
      },
      {
        "id": 3,
        "name": "海鲜3",
        "price": "399.00",
        "image": "/images/products/haixian/haixian3.jpg"
      },
      {
        "id": 4,
        "name": "海鲜4",
        "price": "499.00",
        "image": "/images/products/haixian/haixian4.jpg"
      }
    ],

    // 企业动态
    news: [],

    // 加载状态
    loading: true,
  },

  onLoad() {
    // 设置初始状态为非加载状态，显示静态内容
    this.setData({ loading: false });
    // 异步加载动态数据
    this.loadData();
  },

  onShow() {
    // 页面显示时刷新数据（如果不是首次加载）
    if (this.data.hotProducts.length > 0 || this.data.news.length > 0) {
      this.loadData();
    }
  },

  onPullDownRefresh() {
    this.loadData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载数据
  async loadData() {
    try {
      // 不显示全局loading，避免页面空白
      // this.setData({ loading: true });

      // 并行加载数据，使用默认数据作为fallback
      const [hotProducts, news] = await Promise.all([
        this.loadHotProducts().catch(() => []),
        this.loadNews().catch(() => []),
      ]);

      this.setData({
        hotProducts,
        news,
        loading: false,
      });
    } catch (error) {
      console.error("加载数据失败:", error);
      this.setData({ loading: false });
      // 不显示错误提示，保持页面可用
    }
  },

  // 加载热门产品
  async loadHotProducts() {
    try {
      const data = await get("/api/products/hot", { limit: 6 });
      return data || [];
    } catch (error) {
      console.error("加载热门产品失败:", error);
      // 返回模拟数据作为fallback
      return [
        {
          id: 1,
          name: "精美包装盒",
          price: "2.50",
          image:
            "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjRkY5NTAwIi8+PHJlY3QgeD0iNDAiIHk9IjQwIiB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iI0ZGRjVFRSIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiLz48L3N2Zz4=",
        },
        {
          id: 2,
          name: "环保手提袋",
          price: "1.80",
          image:
            "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMDBCQ0Q0Ii8+PHBhdGggZD0iTTUwIDUwdjEwMGgxMDBWNTB6IiBmaWxsPSIjRkZGNUVFIiBzdHJva2U9IiMzMzMzMzMiIHN0cm9rZS13aWR0aD0iMiIvPjwvc3ZnPg==",
        },
      ];
    }
  },

  // 加载企业动态
  async loadNews() {
    try {
      const data = await get("/api/news", { limit: 3 });
      return data || [];
    } catch (error) {
      console.error("加载企业动态失败:", error);
      // 返回模拟数据作为fallback
      return [
        {
          id: 1,
          title: "济南小森新品发布",
          summary: "我们推出了全新的环保包装系列产品...",
          date: "2024-08-08",
          image:
            "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMTBCOTgxIi8+PHRleHQgeD0iMTAwIiB5PSI4MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5paw5ZOB5Y+R5biDPC90ZXh0Pjwvc3ZnPg==",
        },
      ];
    }
  },

  // 轮播图点击
  onBannerTap(e) {
    const { link } = e.currentTarget.dataset;
    if (link) {
      // 检查是否是tabBar页面
      const tabBarPages = [
        "pages/products/products",
        "pages/calculator/calculator",
        "pages/profile/profile",
      ];
      const targetPage = link.replace("/", "");

      if (tabBarPages.includes(targetPage)) {
        wx.switchTab({
          url: link,
        });
      } else {
        wx.navigateTo({
          url: link,
        });
      }
    }
  },

  // 快速入口点击
  onQuickEntryTap(e) {
    const { link } = e.currentTarget.dataset;
    if (link === "/pages/contact/contact") {
      // 联系客服特殊处理
      this.contactService();
    } else {
      // 检查是否是tabBar页面
      const tabBarPages = [
        "/pages/products/products",
        "/pages/calculator/calculator",
        "/pages/profile/profile",
      ];

      if (tabBarPages.includes(link)) {
        wx.switchTab({
          url: link,
        });
      } else {
        wx.navigateTo({
          url: link,
        });
      }
    }
  },

  // 产品分类点击
  onCategoryTap(e) {
    const { id, name } = e.currentTarget.dataset;
    // 切换到产品tab并传参
    wx.switchTab({ url: "/pages/products/products" });
    // 使用事件总线或全局变量在切换后设置分类
    const app = getApp();
    app.globalData.pendingCategory = { id, name };
  },

  // 热门产品点击
  onProductTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${id}`,
    });
  },

  // 新闻点击
  onNewsTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/news-detail/news-detail?id=${id}`,
    });
  },

  // 查看更多产品
  onMoreProducts() {
    wx.switchTab({
      url: "/pages/products/products",
    });
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ["在线客服", "电话咨询", "微信客服"],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 在线客服
            wx.navigateTo({
              url: "/pages/contact/contact",
            });
            break;
          case 1:
            // 电话咨询
            wx.makePhoneCall({
              phoneNumber: "18854121057",
            });
            break;
          case 2:
            // 微信客服
            wx.showToast({
              title: "请添加微信：xiaosen2024",
              icon: "none",
              duration: 3000,
            });
            break;
        }
      },
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: "济南小森 - 专业包装贸易服务",
      path: "/pages/index/index",
      imageUrl: "/images/share.jpg",
    };
  },
});
