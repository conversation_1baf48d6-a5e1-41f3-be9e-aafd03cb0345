/* 首页样式 */

/* 轮播图区域 */
.banner-section {
  margin-bottom: 32rpx;
}

.banner-swiper {
  height: 400rpx;
  border-radius: 0 0 32rpx 32rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 60rpx 32rpx 32rpx;
}

.banner-title {
  color: var(--white);
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 快速入口区域 */
.quick-section {
  padding: 0 32rpx;
  margin-bottom: 32rpx;
}

.quick-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background-color: var(--white);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.quick-item:active {
  transform: scale(0.95);
}

.quick-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
}

.quick-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.quick-subtitle {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.section-more {
  font-size: 24rpx;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
}

/* 产品分类区域 */
.category-section {
  margin-bottom: 32rpx;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  padding: 0 32rpx;
}

.category-item {
  background-color: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease;
}

.category-item:active {
  transform: scale(0.95);
}

.category-image {
  width: 100%;
  height: 160rpx;
}

.category-info {
  padding: 24rpx;
  text-align: center;
}

.category-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.category-count {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 热门产品区域 */
.product-section {
  margin-bottom: 32rpx;
}

.product-scroll {
  white-space: nowrap;
}

.product-list {
  display: flex;
  padding: 0 32rpx;
  gap: 24rpx;
}

/* 企业动态区域 */
.news-section {
  margin-bottom: 32rpx;
}

.news-list {
  padding: 0 32rpx;
}

.news-item {
  display: flex;
  background-color: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
  margin-bottom: 24rpx;
  transition: transform 0.3s ease;
}

.news-item:active {
  transform: scale(0.98);
}

.news-image {
  width: 200rpx;
  height: 150rpx;
  flex-shrink: 0;
}

.news-content {
  flex: 1;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-title {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-summary {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.news-date {
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 底部间距 */
.bottom-space {
  height: 120rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .quick-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .category-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 加载动画 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: var(--text-secondary);
}

.loading::before {
  content: "";
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
