<!-- 首页 -->
<view class="page-container">
  <!-- 轮播图 -->
  <view class="banner-section">
    <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
      <swiper-item wx:for="{{banners}}" wx:key="id" bindtap="onBannerTap" data-link="{{item.link}}">
        <image class="banner-image" src="{{item.image}}" mode="aspectFill" />
        <view class="banner-overlay">
          <text class="banner-title">{{item.title}}</text>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快速入口 -->
  <view class="quick-section">
    <view class="quick-grid">
      <view 
        class="quick-item" 
        wx:for="{{quickEntries}}" 
        wx:key="id"
        bindtap="onQuickEntryTap"
        data-link="{{item.link}}"
      >
        <image class="quick-icon" src="{{item.icon}}" />
        <text class="quick-title">{{item.title}}</text>
        <text class="quick-subtitle">{{item.subtitle}}</text>
      </view>
    </view>
  </view>

  <!-- 产品分类 -->
  <view class="category-section">
    <view class="section-header">
      <text class="section-title">产品分类</text>
      <view class="section-more" bindtap="onMoreProducts">
        <text>查看全部</text>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
    <view class="category-grid">
      <view 
        class="category-item" 
        wx:for="{{categories}}" 
        wx:key="id"
        bindtap="onCategoryTap"
        data-id="{{item.id}}"
        data-name="{{item.name}}"
      >
        <image class="category-image" src="{{item.image}}" mode="aspectFill" />
        <view class="category-info">
          <text class="category-name">{{item.name}}</text>
          <text class="category-count">{{item.count}}款产品</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 热门产品 -->
  <view class="product-section" wx:if="{{hotProducts.length > 0}}">
    <view class="section-header">
      <text class="section-title">热门产品</text>
      <view class="section-more" bindtap="onMoreProducts">
        <text>查看更多</text>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>
    <scroll-view class="product-scroll" scroll-x="{{true}}">
      <view class="product-list">
        <product-item wx:for="{{hotProducts}}" wx:key="id" product="{{item}}" />
      </view>
    </scroll-view>
  </view>

  <!-- 企业动态 -->
  <view class="news-section" wx:if="{{news.length > 0}}">
    <view class="section-header">
      <text class="section-title">企业动态</text>
    </view>
    <view class="news-list">
      <view 
        class="news-item" 
        wx:for="{{news}}" 
        wx:key="id"
        bindtap="onNewsTap"
        data-id="{{item.id}}"
      >
        <image class="news-image" src="{{item.image}}" mode="aspectFill" />
        <view class="news-content">
          <text class="news-title">{{item.title}}</text>
          <text class="news-summary">{{item.summary}}</text>
          <text class="news-date">{{item.date}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-space"></view>
</view>
