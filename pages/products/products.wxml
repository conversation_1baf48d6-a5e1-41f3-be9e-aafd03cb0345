<!-- 产品列表页 -->
<view class="page-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <input
        class="search-input"
        placeholder="搜索产品名称、材质、规格..."
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
      <view class="search-btn" bindtap="onSearchConfirm">
        <text class="iconfont icon-search"></text>
      </view>
    </view>
  </view>

  <!-- 分类导航 -->
  <view class="category-nav">
    <scroll-view class="category-scroll" scroll-x="{{true}}">
      <view class="category-list">
        <view
          class="category-tab {{currentCategoryId === item.id ? 'active' : ''}}"
          wx:for="{{categories}}"
          wx:key="id"
          bindtap="onCategoryTap"
          data-id="{{item.id}}"
        >
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 工具栏 -->
  <view class="toolbar">
    <view class="toolbar-item" bindtap="onSortTap">
      <text class="iconfont icon-sort"></text>
      <text>排序</text>
    </view>
    <view class="toolbar-item" bindtap="onFilterTap">
      <text class="iconfont icon-filter"></text>
      <text>筛选</text>
    </view>
    <view class="toolbar-info">
      共{{products.length}}款产品
    </view>
  </view>

  <!-- 产品列表 -->
  <view class="product-list">
    <view
      class="product-item"
      wx:for="{{products}}"
      wx:key="id"
      bindtap="onProductTap"
      data-id="{{item.id}}"
    >
      <!-- 产品图片 -->
      <view class="product-image-container">
        <image class="product-image" src="{{item.image}}" mode="aspectFill" />
      </view>

      <!-- 产品信息 -->
      <view class="product-info">
        <text class="product-name">{{item.name}}</text>
        <text class="product-desc">{{item.description}}</text>

        <!-- 规格信息 -->
        <view class="product-specs">
          <text class="spec-item" wx:if="{{item.material}}">{{item.material}}</text>
          <text class="spec-item" wx:if="{{item.size}}">{{item.size}}</text>
        </view>

        <!-- 价格和操作 -->
        <view class="product-footer">
          <view class="price-section">
            <text class="price-label">起价</text>
            <text class="price-value">¥{{item.price}}</text>
            <text class="price-unit">/{{item.unit || '件'}}</text>
          </view>
          <view class="action-section">
            <button
              class="btn btn-primary btn-small"
              bindtap="onOrderTap"
              data-id="{{item.id}}"
            >
              立即下单
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 没有更多数据 -->
  <view class="no-more" wx:if="{{!hasMore && products.length > 0}}">
    <text>没有更多产品了</text>
  </view>

  <!-- 空状态 -->
  <view class="empty" wx:if="{{!loading && products.length === 0}}">
    <image class="empty-icon" src="/images/empty/products.png" />
    <text class="empty-text">暂无相关产品</text>
    <button class="btn btn-outline btn-small" bindtap="onSearchClear">
      查看全部产品
    </button>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-space"></view>
</view>
