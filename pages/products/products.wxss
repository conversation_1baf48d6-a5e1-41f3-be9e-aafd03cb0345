/* 产品列表页样式 */

/* 搜索区域 */
.search-section {
  padding: 24rpx 32rpx;
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--gray-100);
  border-radius: 48rpx;
  padding: 0 32rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: var(--text-primary);
}

.search-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

/* 分类导航 */
.category-nav {
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 32rpx;
}

.category-tab {
  flex-shrink: 0;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.category-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  font-weight: 500;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: var(--white);
  border-bottom: 1px solid var(--border-color);
}

.toolbar-item {
  display: flex;
  align-items: center;
  margin-right: 48rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
}

.toolbar-item .iconfont {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.toolbar-info {
  margin-left: auto;
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 产品列表 */
.product-list {
  padding: 24rpx 32rpx;
}

.product-item {
  background-color: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
  margin-bottom: 24rpx;
  transition: transform 0.3s ease;
}

.product-item:active {
  transform: scale(0.98);
}

/* 产品图片区域 */
.product-image-container {
  position: relative;
  height: 400rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-badge {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.product-favorite {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 64rpx;
  height: 64rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  font-size: 32rpx;
}

.product-favorite:active {
  background-color: rgba(255, 255, 255, 1);
  color: var(--error-color);
}

/* 产品信息区域 */
.product-info {
  padding: 32rpx;
}

.product-name {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 规格信息 */
.product-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.spec-item {
  padding: 8rpx 16rpx;
  background-color: var(--gray-100);
  border-radius: 20rpx;
  font-size: 22rpx;
  color: var(--text-secondary);
}

/* 价格和操作区域 */
.product-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-section {
  display: flex;
  align-items: baseline;
}

.price-label {
  font-size: 22rpx;
  color: var(--text-secondary);
  margin-right: 8rpx;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--secondary-color);
}

.price-unit {
  font-size: 22rpx;
  color: var(--text-secondary);
  margin-left: 4rpx;
}

.action-section {
  display: flex;
  gap: 16rpx;
}

/* 加载状态 */
.loading-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: var(--text-secondary);
}

.loading-section::before {
  content: '';
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: var(--text-secondary);
  font-size: 24rpx;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: 32rpx;
}

/* 底部间距 */
.bottom-space {
  height: 120rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .product-info {
    padding: 24rpx;
  }
  
  .product-name {
    font-size: 28rpx;
  }
  
  .price-value {
    font-size: 32rpx;
  }
}

/* 动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
