// 产品列表页
const { get } = require("../../utils/request");

Page({
  data: {
    // 分类列表
    categories: [
      { id: 0, name: "全部" },
      { id: 1, name: "特产箱" },
      { id: 2, name: "海鲜礼盒" },
      { id: 3, name: "月饼礼盒" },
      { id: 4, name: "坚果礼盒" },
      { id: 5, name: "卡盒" },
      { id: 6, name: "手提袋" },
      { id: 7, name: "纸箱" },
    ],

    // 当前选中的分类
    currentCategoryId: 0,

    // 产品列表
    products: [],

    // 分页信息
    page: 1,
    pageSize: 20,
    hasMore: true,

    // 加载状态
    loading: false,
    refreshing: false,

    // 搜索关键词
    searchKeyword: "",

    // 排序方式
    sortType: "default", // default, price_asc, price_desc, sales

    // 筛选条件
    filters: {
      priceRange: "", // 价格区间
      material: "", // 材质
      size: "", // 尺寸
    },
  },

  onLoad(options) {
    // 从参数中获取分类信息
    if (options.categoryId) {
      this.setData({
        currentCategoryId: parseInt(options.categoryId),
      });
    }

    // 先显示本地模拟且按分类/搜索过滤的数据，避免页面空白
    this.setData({
      products: this.getFilteredMockList(),
      loading: false,
    });

    // 然后尝试加载真实数据
    this.loadProducts(true);
  },

  onShow() {
    // 若首页通过全局变量传递了待选分类，则生效
    const app = getApp();
    const pending = app.globalData?.pendingCategory;
    if (pending) {
      this.setData({ currentCategoryId: Number(pending.id) || 0 });
      app.globalData.pendingCategory = null;
      this.loadProducts(true);
      return;
    }

    // 页面显示时刷新数据（如果已有数据则不重新加载）
    if (this.data.products.length === 0) {
      this.setData({
        products: this.getMockProducts(),
        loading: false,
      });
      this.loadProducts(true);
    }
  },

  onReachBottom() {
    // 触底加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadProducts(false);
    }
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadProducts(true).finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 获取模拟产品数据（按业务分类）
  getMockProducts() {
    return [
      {
        id: 101,
        categoryId: 1,
        name: "特产礼盒-红枣阿胶糕",
        description: "电商渠道热销，支持OEM/ODM",
        price: "12.80",
        unit: "套",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nI0Y5OTUwMCcvPjwvc3ZnPi==",
        isHot: true,
      },
      {
        id: 102,
        categoryId: 1,
        name: "地方特产组合箱",
        description: "支持多SKU组合装，物流打包稳",
        price: "18.50",
        unit: "箱",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nI0Y5OTUwMCcvPjwvc3ZnPi==",
      },

      {
        id: 201,
        categoryId: 2,
        name: "海鲜礼盒-三文鱼冻品",
        description: "低温链供应，箱体抗潮耐压",
        price: "56.00",
        unit: "箱",
        image: "/images/products/haixian/haixian1.jpg",
        images: [
          "/images/products/haixian/haixian1.jpg",
          "/images/products/haixian/haixian3.jpg",
          "/images/products/haixian/haixian4.jpg",
        ],
      },
      {
        id: 202,
        categoryId: 2,
        name: "海鲜礼盒-海参鲍鱼",
        description: "礼赠场景，外观商务简洁",
        price: "86.00",
        unit: "箱",
        image: "/images/products/haixian/haixian2.jpg",
        images: [
          "/images/products/haixian/haixian2.jpg",
          "/images/products/haixian/haixian4.jpg",
          "/images/products/haixian/haixian3.jpg",
        ],
      },

      {
        id: 301,
        categoryId: 3,
        name: "月饼礼盒-双层抽屉式",
        description: "中秋定制主推，支持烫金/UV",
        price: "22.00",
        unit: "套",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nIzYzNjZGNycvPjwvc3ZnPi==",
        isHot: true,
      },
      {
        id: 302,
        categoryId: 3,
        name: "月饼礼盒-翻盖磁吸",
        description: "磁吸结构，开合顺滑稳固",
        price: "19.80",
        unit: "套",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nIzYzNjZGNycvPjwvc3ZnPi==",
      },

      {
        id: 401,
        categoryId: 4,
        name: "坚果礼盒-隔仓内托",
        description: "抗震内托，多SKU固定",
        price: "16.50",
        unit: "套",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nIzQwMUQ2NCcvPjwvc3ZnPi==",
      },
      {
        id: 402,
        categoryId: 4,
        name: "坚果礼盒-天地盖",
        description: "经典天地盖结构，性价比高",
        price: "13.90",
        unit: "套",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nI0ZGNjM5NCcvPjwvc3ZnPi==",
      },

      {
        id: 501,
        categoryId: 5,
        name: "卡盒-插舌锁底",
        description: "单品外包装，快速成型",
        price: "0.68",
        unit: "个",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nI0ZGNjM5NCcvPjwvc3ZnPi==",
      },
      {
        id: 502,
        categoryId: 5,
        name: "卡盒-飞机盒",
        description: "物流友好，快装快拆",
        price: "1.20",
        unit: "个",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nIzEwQjk4MScvPjwvc3ZnPi==",
      },

      {
        id: 601,
        categoryId: 6,
        name: "手提袋-白卡纸",
        description: "商务风，扁绳/棉绳可选",
        price: "1.80",
        unit: "个",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nI0UxRTNFQScvPjwvc3ZnPi==",
      },
      {
        id: 602,
        categoryId: 6,
        name: "手提袋-牛皮纸",
        description: "环保质感，承重更强",
        price: "1.50",
        unit: "个",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNjAwJyBoZWlnaHQ9JzQwMCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nI0U5RUE4OCcvPjwvc3ZnPi==",
      },

      {
        id: 701,
        categoryId: 7,
        name: "物流纸箱-三层特硬",
        description: "3层K=A加硬，抗压更稳",
        price: "2.30",
        unit: "个",
        image: "/images/products/box/box2.jpg",
        images: [
          "/images/products/box/box1.jpg",
          "/images/products/box/box2.jpg",
        ],
      },
      {
        id: 702,
        categoryId: 7,
        name: "物流纸箱-五层加厚",
        description: "5层K=K加厚，大件稳固",
        price: "3.60",
        unit: "个",
        image: "/images/products/box/box1.jpg",
        images: [
          "/images/products/box/box1.jpg",
          "/images/products/box/box2.jpg",
        ],
      },
    ];
  },

  // 依据当前分类/关键词返回展示列表（本地Mock）
  getFilteredMockList() {
    const all = this.getMockProducts();
    let list = all;
    if (this.data.currentCategoryId && this.data.currentCategoryId !== 0) {
      list = list.filter((p) => p.categoryId === this.data.currentCategoryId);
    }
    if (this.data.searchKeyword) {
      const k = this.data.searchKeyword.trim();
      list = list.filter(
        (p) => p.name.includes(k) || (p.description || "").includes(k)
      );
    }
    return list;
  },

  // 加载产品列表
  async loadProducts(reset = false) {
    if (this.data.loading) return;

    this.setData({ loading: true });

    try {
      const page = reset ? 1 : this.data.page + 1;
      const params = {
        page,
        pageSize: this.data.pageSize,
        categoryId: this.data.currentCategoryId || undefined,
        keyword: this.data.searchKeyword || undefined,
        sortType: this.data.sortType,
        ...this.data.filters,
      };

      // 优先使用本地Mock，保证无网络也有数据
      const local = this.getFilteredMockList();
      const start = reset ? 0 : this.data.page * this.data.pageSize;
      const slice = local.slice(start, start + this.data.pageSize);

      this.setData({
        products: reset ? slice : [...this.data.products, ...slice],
        page,
        hasMore: start + this.data.pageSize < local.length,
        loading: false,
      });
    } catch (error) {
      console.error("加载产品失败:", error);
      // 使用本地Mock兜底
      const local = this.getFilteredMockList();
      const start = reset ? 0 : this.data.page * this.data.pageSize;
      const slice = local.slice(start, start + this.data.pageSize);
      this.setData({
        products: reset ? slice : [...this.data.products, ...slice],
        page,
        hasMore: start + this.data.pageSize < local.length,
        loading: false,
      });
    }
  },

  // 分类切换
  onCategoryTap(e) {
    const categoryId = e.currentTarget.dataset.id;
    if (categoryId === this.data.currentCategoryId) return;

    this.setData({
      currentCategoryId: categoryId,
      page: 1,
    });
    this.loadProducts(true);
  },

  // 搜索
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value,
    });
  },

  onSearchConfirm() {
    this.setData({ page: 1 });
    this.loadProducts(true);
  },

  onSearchClear() {
    this.setData({
      searchKeyword: "",
      page: 1,
    });
    this.loadProducts(true);
  },

  // 排序
  onSortTap() {
    const sortOptions = [
      { key: "default", name: "默认排序" },
      { key: "price_asc", name: "价格从低到高" },
      { key: "price_desc", name: "价格从高到低" },
      { key: "sales", name: "销量优先" },
    ];

    wx.showActionSheet({
      itemList: sortOptions.map((item) => item.name),
      success: (res) => {
        const selectedSort = sortOptions[res.tapIndex];
        if (selectedSort.key !== this.data.sortType) {
          this.setData({
            sortType: selectedSort.key,
            page: 1,
          });
          this.loadProducts(true);
        }
      },
    });
  },

  // 筛选
  onFilterTap() {
    wx.navigateTo({
      url: "/pages/product-filter/product-filter",
    });
  },

  // 产品点击（优先携带本地产品对象，确保详情价格/图片一致）
  onProductTap(e) {
    const { id } = e.currentTarget.dataset;
    const item = this.data.products.find((p) => p.id === id);
    if (item) {
      const productData = encodeURIComponent(JSON.stringify(item));
      wx.navigateTo({
        url: `/pages/product-detail/product-detail?productData=${productData}`,
      });
    } else {
      wx.navigateTo({ url: `/pages/product-detail/product-detail?id=${id}` });
    }
  },

  // 立即下单（优先本地数据直达，避免接口失败）
  onOrderTap(e) {
    e.stopPropagation();
    const { id } = e.currentTarget.dataset;
    const item = this.data.products.find((p) => p.id === id);
    if (item) {
      const orderData = {
        product: item,
        quantity: item.minQuantity || 1,
        specifications: {},
      };
      const params = encodeURIComponent(JSON.stringify(orderData));
      wx.navigateTo({ url: `/pages/order/order?orderData=${params}` });
    } else {
      // 兜底
      wx.navigateTo({ url: `/pages/order/order?productId=${id}` });
    }
  },

  // 添加到收藏
  onFavoriteTap(e) {
    e.stopPropagation();
    const { id, index } = e.currentTarget.dataset;

    // TODO: 调用收藏API
    wx.showToast({
      title: "已添加到收藏",
      icon: "success",
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: "小森包装 - 专业印刷包装产品",
      path: "/pages/products/products",
      imageUrl: "/images/share-products.jpg",
    };
  },
});
