/* 关于我们页面样式 */

/* 公司信息区域 */
.company-info {
  margin: 20rpx 24rpx;
}

.company-header {
  text-align: center;
  padding: 28rpx 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 32rpx;
}

.company-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 16rpx;
}

.company-established {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 联系方式列表 */
.contact-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: var(--gray-50);
  border-radius: 12rpx;
  transition: background-color 0.3s ease;
}

.contact-item:active {
  background-color: var(--gray-100);
}

.contact-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.contact-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.contact-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 8rpx;
}

.contact-value {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
}

/* 区域标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 32rpx;
  text-align: center;
}

/* 企业优势区域 */
.advantages-section {
  margin: 20rpx 24rpx;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.advantage-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 32rpx 16rpx;
  background-color: var(--gray-50);
  border-radius: 16rpx;
}

.advantage-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.advantage-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.advantage-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 发展历程区域 */
.milestones-section {
  margin: 20rpx 24rpx;
}

.timeline {
  position: relative;
  padding-left: 80rpx;
}

.timeline::before {
  content: "";
  position: absolute;
  left: 40rpx;
  top: 0;
  bottom: 0;
  width: 4rpx;
  background-color: var(--border-color);
}

.timeline-item {
  position: relative;
  margin-bottom: 48rpx;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-year {
  position: absolute;
  left: -80rpx;
  top: 0;
  width: 80rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: var(--primary-color);
  text-align: center;
}

.timeline-content {
  position: relative;
  padding-left: 32rpx;
}

.timeline-dot {
  position: absolute;
  left: -42rpx;
  top: 8rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  border: 4rpx solid var(--white);
  box-shadow: 0 0 0 4rpx var(--border-color);
}

.timeline-event {
  font-size: 26rpx;
  color: var(--text-primary);
  line-height: 1.6;
}

/* 联系我们区域 */
.contact-section {
  margin: 20rpx 24rpx;
}

.contact-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.contact-btn.primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.contact-btn.primary:active {
  background-color: #1e40af;
}

.contact-btn.secondary {
  background-color: var(--gray-100);
  color: var(--text-primary);
}

.contact-btn.secondary:active {
  background-color: var(--gray-200);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .timeline {
    padding-left: 60rpx;
  }

  .timeline-year {
    left: -60rpx;
    width: 60rpx;
    font-size: 22rpx;
  }

  .timeline-dot {
    left: -32rpx;
  }
}
