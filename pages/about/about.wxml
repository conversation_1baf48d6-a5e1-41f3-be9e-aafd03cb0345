<!-- 关于我们页面 -->
<view class="page-container">
  <!-- 公司信息 -->
  <view class="company-info card">
    <view class="company-header">
      <text class="company-name">{{companyInfo.name}}</text>
      <text class="company-established">成立于{{companyInfo.established}}</text>
    </view>

    <view class="contact-list">
      <view class="contact-item" bindtap="onCallPhone">
        <text class="contact-icon">📞</text>
        <view class="contact-content">
          <text class="contact-label">联系电话</text>
          <text class="contact-value">{{companyInfo.phone}}</text>
        </view>
      </view>

      <view class="contact-item" bindtap="onCopyEmail">
        <text class="contact-icon">📧</text>
        <view class="contact-content">
          <text class="contact-label">邮箱地址</text>
          <text class="contact-value">{{companyInfo.email}}</text>
        </view>
      </view>

      <view class="contact-item" bindtap="onViewLocation">
        <text class="contact-icon">📍</text>
        <view class="contact-content">
          <text class="contact-label">公司地址</text>
          <text class="contact-value">{{companyInfo.address}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 企业优势 -->
  <view class="advantages-section card">
    <view class="section-title">企业优势</view>
    <view class="advantages-grid">
      <view class="advantage-item" wx:for="{{advantages}}" wx:key="title">
        <image class="advantage-icon" src="{{item.icon}}" mode="aspectFit" />
        <text class="advantage-title">{{item.title}}</text>
        <text class="advantage-desc">{{item.desc}}</text>
      </view>
    </view>
  </view>

  <!-- 发展历程 -->
  <view class="milestones-section card">
    <view class="section-title">发展历程</view>
    <view class="timeline">
      <view class="timeline-item" wx:for="{{milestones}}" wx:key="year">
        <view class="timeline-year">{{item.year}}</view>
        <view class="timeline-content">
          <view class="timeline-dot"></view>
          <text class="timeline-event">{{item.event}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系我们 -->
  <view class="contact-section card">
    <view class="section-title">联系我们</view>
    <view class="contact-actions">
      <button class="contact-btn primary" bindtap="onCallPhone">
        <text class="btn-icon">📞</text>
        <text>立即致电</text>
      </button>
      <button class="contact-btn secondary" bindtap="onCopyEmail">
        <text class="btn-icon">📧</text>
        <text>发送邮件</text>
      </button>
      <button class="contact-btn secondary" bindtap="onViewLocation">
        <text class="btn-icon">📍</text>
        <text>查看位置</text>
      </button>
    </view>
  </view>
</view>