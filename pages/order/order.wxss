/* 立即下单页面样式 */

.page-container {
  background-color: #f8fafc;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f4f6;
  border-top: 6rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 卡片样式 */
.card {
  background-color: var(--white);
  margin: 20rpx 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 区域标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #f1f5f9;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.section-action {
  font-size: 26rpx;
  color: var(--primary-color);
}

/* 产品信息 */
.product-info {
  display: flex;
  align-items: center;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.product-details {
  flex: 1;
}

.product-name {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.product-desc {
  display: block;
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 12rpx;
}

.product-price {
  display: flex;
  align-items: baseline;
}

.price-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.price-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--secondary-color);
  margin: 0 8rpx;
}

.price-unit {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 收货地址 */
.address-content {
  padding: 24rpx;
  background-color: #f8fafc;
  border-radius: 12rpx;
  border: 2rpx solid #e2e8f0;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.contact-name {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 16rpx;
}

.contact-phone {
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-right: 16rpx;
}

.default-tag {
  padding: 4rpx 12rpx;
  background-color: var(--secondary-color);
  color: var(--white);
  font-size: 20rpx;
  border-radius: 20rpx;
}

.address-detail {
  font-size: 26rpx;
  color: var(--text-primary);
  line-height: 1.5;
}

.address-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
}

/* 订单信息 */
.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1px solid #f1f5f9;
}

.order-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
  min-width: 120rpx;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid #e2e8f0;
  border-radius: 8rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f8fafc;
  border: none;
  font-size: 28rpx;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:active {
  background-color: #e2e8f0;
}

.quantity-input {
  width: 100rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background-color: var(--white);
}

/* 文本输入框 */
.custom-textarea,
.remarks-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #f8fafc;
}

/* 选择器 */
.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 200rpx;
  padding: 16rpx 20rpx;
  background-color: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 8rpx;
}

.picker-text {
  font-size: 26rpx;
  color: var(--text-primary);
}

.picker-arrow {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 优惠券 */
.coupon-content {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

.coupon-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.coupon-info {
  flex: 1;
}

.coupon-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.coupon-arrow {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 支付方式 */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.payment-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.payment-item.selected {
  border-color: var(--primary-color);
  background-color: #eff6ff;
}

.payment-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.payment-name {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-primary);
}

.payment-radio {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-item.selected .payment-radio {
  border-color: var(--primary-color);
}

.radio-inner {
  width: 16rpx;
  height: 16rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
}

/* 价格明细 */
.price-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 0;
}

.price-item.total {
  padding-top: 16rpx;
  border-top: 1px solid #e2e8f0;
  font-weight: bold;
}

.price-item .price-label {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.price-item.total .price-label {
  font-size: 28rpx;
  color: var(--text-primary);
}

.price-item .price-value {
  font-size: 26rpx;
  color: var(--text-primary);
}

.price-item.total .price-value {
  font-size: 32rpx;
  color: var(--secondary-color);
}

.price-value.discount {
  color: var(--secondary-color);
}

/* 底部提交区域 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background-color: var(--white);
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.submit-info {
  flex: 1;
  margin-right: 24rpx;
}

.total-label {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.total-price {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--secondary-color);
}

.submit-btn {
  flex: 0 0 240rpx;
  height: 88rpx;
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  color: var(--white);
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn:not(:disabled):active {
  transform: scale(0.98);
}

.submit-btn.submitting {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
}

.submit-btn:disabled {
  opacity: 0.6;
}
