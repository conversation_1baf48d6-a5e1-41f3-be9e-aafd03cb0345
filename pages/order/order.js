// 下单页面
const app = getApp();
const { get, post } = require("../../utils/request");
const { formatPrice, showModal } = require("../../utils/util");

Page({
  data: {
    // 订单类型：product(产品下单) | calculator(计算器下单) | cart(购物车下单)
    orderType: "product",

    // 产品信息
    product: null,

    // 订单商品列表
    orderItems: [],

    // 收货地址
    selectedAddress: null,

    // 订单信息
    orderInfo: {
      quantity: 1,
      specifications: {},
      customRequirements: "", // 定制需求
      urgentLevel: "normal", // 加急程度
      deliveryDate: "", // 期望交期
      remarks: "", // 备注
    },

    // 价格信息
    priceDetail: {
      subtotal: 0, // 小计
      urgentFee: 0, // 加急费
      deliveryFee: 0, // 运费
      discountAmount: 0, // 优惠金额
      totalAmount: 0, // 总金额
    },

    // 优惠券
    availableCoupons: [],
    selectedCoupon: null,

    // 加急选项
    urgentOptions: [
      { id: "normal", name: "正常(7-10天)", fee: 0, multiplier: 1.0 },
      { id: "urgent", name: "加急(3-5天)", fee: 0, multiplier: 1.3 },
      { id: "super_urgent", name: "特急(1-2天)", fee: 0, multiplier: 1.8 },
    ],
    urgentIndex: 0,

    // 支付方式
    paymentMethods: [
      { id: "wechat", name: "微信支付", icon: "/images/icons/wechat-pay.png" },
      { id: "alipay", name: "支付宝", icon: "/images/icons/alipay.png" },
      { id: "bank", name: "银行转账", icon: "/images/icons/bank.png" },
    ],
    selectedPayment: "wechat",

    // 加急picker索引与显示名
    urgentIndex: 0,
    urgentLevelName: "",

    // 文件上传
    uploadedFiles: [],

    // 加载状态
    loading: false,
    submitting: false,
  },

  onLoad(options) {
    // 解析参数
    if (options.productId) {
      this.setData({ orderType: "product" });
      this.loadProductOrder(options.productId);
    } else if (options.calculatorParams) {
      this.setData({ orderType: "calculator" });
      this.loadCalculatorOrder(options.calculatorParams);
    } else if (options.cartItems) {
      this.setData({ orderType: "cart" });
      this.loadCartOrder(options.cartItems);
    } else if (options.orderData) {
      this.loadOrderData(options.orderData);
    }

    // 加载用户地址
    this.loadUserAddress();

    // 加载优惠券
    this.loadAvailableCoupons();
  },

  // 加载产品下单信息
  async loadProductOrder(productId) {
    try {
      this.setData({ loading: true });
      const product = await get(`/api/products/${productId}`);

      this.setData({
        product,
        "orderInfo.quantity": product.minQuantity || 1,
        loading: false,
      });

      this.calculatePrice();
    } catch (error) {
      console.error("加载产品信息失败:", error);
      // 使用本地兜底产品，避免“加载失败”体验
      const fallback = {
        id: Number(productId),
        name: "定制商品",
        description: "根据您的下单参数进行定制生产",
        price: "2.50",
        unit: "件",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI0Y4RkFGQyIvPjwvc3ZnPi==",
        minQuantity: 1,
      };
      this.setData({
        product: fallback,
        "orderInfo.quantity": fallback.minQuantity,
        loading: false,
      });
      this.calculatePrice();
    }
  },

  // 加载计算器下单信息
  loadCalculatorOrder(paramsStr) {
    try {
      const params = JSON.parse(decodeURIComponent(paramsStr));
      this.setData({
        orderInfo: {
          ...this.data.orderInfo,
          quantity: Number(params.quantity) || 1,
          urgentLevel: params.urgentLevel || "normal",
          specifications: params.specifications || {},
          customRequirements: params.customRequirements || "",
        },
      });
      this.calculatePrice();
    } catch (error) {
      console.error("解析计算器参数失败:", error);
    }
  },

  // 加载订单数据
  loadOrderData(orderDataStr) {
    try {
      const orderData = JSON.parse(decodeURIComponent(orderDataStr));
      this.setData({
        product: orderData.product,
        orderInfo: {
          ...this.data.orderInfo,
          quantity: orderData.quantity,
          specifications: orderData.specifications,
        },
        priceDetail: orderData.priceInfo || this.data.priceDetail,
      });
    } catch (error) {
      console.error("解析订单数据失败:", error);
    }
  },

  // 加载用户地址
  async loadUserAddress() {
    try {
      const addresses = await get("/api/user/addresses");
      const defaultAddress =
        addresses.find((addr) => addr.isDefault) || addresses[0];

      // 兜底一个本地地址，避免页面提示“加载失败”
      this.setData({
        selectedAddress: defaultAddress || {
          id: 0,
          name: "收件人",
          phone: "13800000000",
          address: "山东省济南市历下区XX路XX号",
          isDefault: true,
        },
      });
    } catch (error) {
      console.error("加载地址失败:", error);
    }
  },

  // 加载可用优惠券
  async loadAvailableCoupons() {
    try {
      const coupons = await get("/api/user/coupons/available");
      this.setData({ availableCoupons: coupons });
    } catch (error) {
      console.error("加载优惠券失败:", error);
    }
  },

  // 选择收货地址
  onSelectAddress() {
    wx.navigateTo({
      url: "/pages/address/address?mode=select",
    });
  },

  // 数量变化
  onQuantityChange(e) {
    const { type } = e.currentTarget.dataset;
    let quantity = this.data.orderInfo.quantity;
    const minQuantity = this.data.product?.minQuantity || 1;

    if (type === "minus" && quantity > minQuantity) {
      quantity--;
    } else if (type === "plus") {
      quantity++;
    }

    this.setData({
      "orderInfo.quantity": quantity,
    });
    this.calculatePrice();
  },

  // 数量输入
  onQuantityInput(e) {
    const quantity = parseInt(e.detail.value) || 1;
    this.setData({
      "orderInfo.quantity": quantity,
    });
    this.calculatePrice();
  },

  // 定制需求输入
  onRequirementsInput(e) {
    this.setData({
      "orderInfo.customRequirements": e.detail.value,
    });
  },

  // 选择加急程度
  // 加急picker绑定为change事件，更新索引与值
  onUrgentSelect(e) {
    const index = Number(e?.detail?.value ?? 0);
    const selected =
      this.data.urgentOptions[index] || this.data.urgentOptions[0];
    this.setData({
      urgentIndex: index,
      "orderInfo.urgentLevel": selected.id,
    });
    this.calculatePrice();
  },

  // 期望交期选择
  onDeliveryDateChange(e) {
    this.setData({
      "orderInfo.deliveryDate": e.detail.value,
    });
  },

  // 备注输入
  onRemarksInput(e) {
    this.setData({
      "orderInfo.remarks": e.detail.value,
    });
  },

  // 选择优惠券
  onSelectCoupon() {
    if (this.data.availableCoupons.length === 0) {
      wx.showToast({
        title: "暂无可用优惠券",
        icon: "none",
      });
      return;
    }

    wx.navigateTo({
      url: "/pages/coupon-select/coupon-select",
    });
  },

  // 选择支付方式
  onPaymentSelect(e) {
    const { id } = e.currentTarget.dataset;
    this.setData({ selectedPayment: id });
  },

  // 计算价格
  async calculatePrice() {
    try {
      const params = {
        productId: this.data.product?.id,
        quantity: this.data.orderInfo.quantity,
        specifications: this.data.orderInfo.specifications,
        urgentLevel: this.data.orderInfo.urgentLevel,
        couponId: this.data.selectedCoupon?.id,
      };

      // 使用本地模拟价格计算，避免网络失败
      const priceDetail = this.mockCalculatePrice(params);

      this.setData({
        priceDetail: {
          subtotal: formatPrice(priceDetail.subtotal),
          urgentFee: formatPrice(priceDetail.urgentFee),
          deliveryFee: formatPrice(priceDetail.deliveryFee),
          discountAmount: formatPrice(priceDetail.discountAmount),
          totalAmount: formatPrice(priceDetail.totalAmount),
        },
      });
    } catch (error) {
      console.error("计算价格失败:", error);
      // 失败也不阻断UI，使用上次计算结果或0值
      if (!this.data.priceDetail) {
        this.setData({
          priceDetail: {
            subtotal: 0,
            urgentFee: 0,
            deliveryFee: 0,
            discountAmount: 0,
            totalAmount: 0,
          },
        });
      }
    }
  },

  // 本地模拟价格计算
  mockCalculatePrice({ quantity = 1, urgentLevel, couponId }) {
    const unitPrice = 2.5; // 基础单价
    const subtotal = unitPrice * quantity;
    const urgentMap = { normal: 1.0, urgent: 1.3, super_urgent: 1.8 };
    const urgentFee = subtotal * ((urgentMap[urgentLevel] || 1) - 1);
    const deliveryFee = subtotal >= 500 ? 0 : 20;
    const discountAmount = couponId ? 50 : 0;
    const totalAmount = Math.max(
      0,
      subtotal + urgentFee + deliveryFee - discountAmount
    );
    return { subtotal, urgentFee, deliveryFee, discountAmount, totalAmount };
  },

  // 提交订单
  async onSubmitOrder() {
    // 验证必填信息
    if (!this.validateOrder()) {
      return;
    }

    const confirmed = await showModal({
      title: "确认下单",
      content: `订单总金额：¥${this.data.priceDetail.totalAmount}，确认提交订单吗？`,
    });

    if (!confirmed) return;

    try {
      this.setData({ submitting: true });

      const orderData = {
        orderType: this.data.orderType,
        productId: this.data.product?.id,
        orderInfo: this.data.orderInfo,
        addressId: this.data.selectedAddress?.id,
        couponId: this.data.selectedCoupon?.id,
        paymentMethod: this.data.selectedPayment,
        uploadedFiles: this.data.uploadedFiles,
      };

      const result = await post("/api/orders/create", orderData);

      this.setData({ submitting: false });

      // 跳转到支付页面或订单详情
      wx.redirectTo({
        url: `/pages/order-detail/order-detail?id=${result.orderId}`,
      });
    } catch (error) {
      this.setData({ submitting: false });
      console.error("提交订单失败:", error);
      wx.showToast({
        title: "提交失败",
        icon: "none",
      });
    }
  },

  // 验证订单信息
  validateOrder() {
    if (!this.data.selectedAddress) {
      wx.showToast({
        title: "请选择收货地址",
        icon: "none",
      });
      return false;
    }

    if (!this.data.orderInfo.quantity || this.data.orderInfo.quantity < 1) {
      wx.showToast({
        title: "请输入正确的数量",
        icon: "none",
      });
      return false;
    }

    return true;
  },
});
