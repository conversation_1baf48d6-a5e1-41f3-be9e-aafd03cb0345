<!-- 立即下单页面 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else>
    <!-- 产品信息 -->
    <view class="product-section card" wx:if="{{product}}">
      <view class="section-header">
        <text class="section-title">产品信息</text>
      </view>
      <view class="product-info">
        <image class="product-image" src="{{product.image}}" mode="aspectFill" />
        <view class="product-details">
          <text class="product-name">{{product.name}}</text>
          <text class="product-desc">{{product.description}}</text>
          <view class="product-price">
            <text class="price-label">单价：</text>
            <text class="price-value">¥{{product.price}}</text>
            <text class="price-unit">/{{product.unit}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address-section card">
      <view class="section-header" bindtap="onSelectAddress">
        <text class="section-title">收货地址</text>
        <text class="section-action">{{selectedAddress ? '更换' : '选择'}}</text>
      </view>

      <view class="address-content" wx:if="{{selectedAddress}}">
        <view class="address-info">
          <view class="address-header">
            <text class="contact-name">{{selectedAddress.name}}</text>
            <text class="contact-phone">{{selectedAddress.phone}}</text>
            <text class="default-tag" wx:if="{{selectedAddress.isDefault}}">默认</text>
          </view>
          <text class="address-detail">{{selectedAddress.address}}</text>
        </view>
      </view>

      <view class="address-empty" wx:else bindtap="onSelectAddress">
        <text class="empty-icon">📍</text>
        <text class="empty-text">请选择收货地址</text>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-section card">
      <view class="section-header">
        <text class="section-title">订单信息</text>
      </view>

      <!-- 数量选择 -->
      <view class="order-item">
        <text class="item-label">数量</text>
        <view class="quantity-control">
          <button class="quantity-btn" bindtap="onQuantityChange" data-type="minus">-</button>
          <input class="quantity-input" type="number" value="{{orderInfo.quantity}}" bindinput="onQuantityInput" />
          <button class="quantity-btn" bindtap="onQuantityChange" data-type="plus">+</button>
        </view>
      </view>

      <!-- 定制需求 -->
      <view class="order-item">
        <text class="item-label">定制需求</text>
        <textarea
          class="custom-textarea"
          placeholder="请描述您的定制需求（可选）"
          value="{{orderInfo.customRequirements}}"
          bindinput="onRequirementsInput"
          maxlength="200"
        />
      </view>

      <!-- 加急程度 -->
      <view class="order-item">
        <text class="item-label">加急程度</text>
        <picker range="{{urgentOptions}}" range-key="name" value="{{urgentIndex}}" bindchange="onUrgentSelect">
          <view class="picker-display">
            <text class="picker-text">{{(urgentOptions[urgentIndex] && urgentOptions[urgentIndex].name) || '请选择加急程度'}}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 期望交期 -->
      <view class="order-item">
        <text class="item-label">期望交期</text>
        <picker mode="date" value="{{orderInfo.deliveryDate}}" bindchange="onDeliveryDateChange">
          <view class="picker-display">
            <text class="picker-text">{{orderInfo.deliveryDate || '请选择期望交期'}}</text>
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>

      <!-- 备注 -->
      <view class="order-item">
        <text class="item-label">备注</text>
        <textarea
          class="remarks-textarea"
          placeholder="其他备注信息（可选）"
          value="{{orderInfo.remarks}}"
          bindinput="onRemarksInput"
          maxlength="100"
        />
      </view>
    </view>

    <!-- 优惠券 -->
    <view class="coupon-section card" bindtap="onSelectCoupon">
      <view class="coupon-content">
        <text class="coupon-icon">🎫</text>
        <view class="coupon-info">
          <text class="coupon-title">优惠券</text>
          <text class="coupon-desc" wx:if="{{selectedCoupon}}">已选择：{{selectedCoupon.name}}</text>
          <text class="coupon-desc" wx:else>{{availableCoupons.length}}张可用</text>
        </view>
        <text class="coupon-arrow">></text>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="payment-section card">
      <view class="section-header">
        <text class="section-title">支付方式</text>
      </view>
      <view class="payment-methods">
        <view
          class="payment-item {{selectedPayment === item.id ? 'selected' : ''}}"
          wx:for="{{paymentMethods}}"
          wx:key="id"
          bindtap="onPaymentSelect"
          data-id="{{item.id}}"
        >
          <image class="payment-icon" src="{{item.icon}}" />
          <text class="payment-name">{{item.name}}</text>
          <view class="payment-radio">
            <view class="radio-inner" wx:if="{{selectedPayment === item.id}}"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 价格明细 -->
    <view class="price-section card">
      <view class="section-header">
        <text class="section-title">价格明细</text>
      </view>
      <view class="price-details">
        <view class="price-item">
          <text class="price-label">商品小计</text>
          <text class="price-value">¥{{priceDetail.subtotal}}</text>
        </view>
        <view class="price-item" wx:if="{{priceDetail.urgentFee > 0}}">
          <text class="price-label">加急费用</text>
          <text class="price-value">¥{{priceDetail.urgentFee}}</text>
        </view>
        <view class="price-item">
          <text class="price-label">运费</text>
          <text class="price-value">¥{{priceDetail.deliveryFee}}</text>
        </view>
        <view class="price-item" wx:if="{{priceDetail.discountAmount > 0}}">
          <text class="price-label">优惠金额</text>
          <text class="price-value discount">-¥{{priceDetail.discountAmount}}</text>
        </view>
        <view class="price-item total">
          <text class="price-label">总计</text>
          <text class="price-value">¥{{priceDetail.totalAmount}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部提交按钮 -->
  <view class="submit-section" wx:if="{{!loading}}">
    <view class="submit-info">
      <text class="total-label">总计：</text>
      <text class="total-price">¥{{priceDetail.totalAmount || '0.00'}}</text>
    </view>
    <button
      class="submit-btn {{submitting ? 'submitting' : ''}}"
      bindtap="onSubmitOrder"
      disabled="{{submitting}}"
    >
      {{submitting ? '提交中...' : '立即下单'}}
    </button>
  </view>
</view>