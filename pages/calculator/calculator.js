// 价格计算器页面
const { post } = require("../../utils/request");
const { formatPrice } = require("../../utils/util");

Page({
  data: {
    // 产品类型（与业务一致）
    productTypes: [
      {
        id: 1,
        name: "特产箱",
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCI+PHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRkY5NTAwIiByeD0iOCIvPjwvc3ZnPi",
      },
      {
        id: 2,
        name: "海鲜礼盒",
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCI+PHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMDBCQ0Q0IiByeD0iOCIvPjwvc3ZnPi",
      },
      {
        id: 3,
        name: "月饼礼盒",
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCI+PHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjNjM2NkY3IiByeD0iOCIvPjwvc3ZnPi",
      },
      {
        id: 4,
        name: "坚果礼盒",
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCI+PHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRkY2Mzk0IiByeD0iOCIvPjwvc3ZnPi",
      },
      {
        id: 5,
        name: "卡盒",
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCI+PHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjMTBCOTgxIiByeD0iOCIvPjwvc3ZnPi",
      },
      {
        id: 6,
        name: "手提袋",
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCI+PHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjOEI1Q0Y2IiByeD0iOCIvPjwvc3ZnPi",
      },
      {
        id: 7,
        name: "纸箱",
        icon: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCI+PHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjNjY3ODkyIiByeD0iOCIvPjwvc3ZnPi",
      },
    ],

    // 当前选中的产品类型
    selectedProductType: null,

    // 计算参数
    calculatorParams: {
      productTypeId: "",
      quantity: 1000, // 数量
      width: "", // 宽度(mm)
      height: "", // 高度(mm)
      depth: "", // 深度(mm)
      material: "", // 材质
      thickness: "", // 厚度
      printType: "", // 印刷方式
      printColors: 4, // 印刷颜色数
      finishType: "", // 后道工艺
      urgentLevel: "normal", // 加急程度
    },

    // 材质选项
    materials: [
      { id: "cardboard", name: "卡纸", price: 0.8 },
      { id: "corrugated", name: "瓦楞纸", price: 1.2 },
      { id: "kraft", name: "牛皮纸", price: 1.0 },
      { id: "coated", name: "铜版纸", price: 1.5 },
      { id: "art", name: "艺术纸", price: 2.0 },
    ],

    // 印刷方式
    printTypes: [
      { id: "offset", name: "胶印", price: 1.0 },
      { id: "digital", name: "数码印刷", price: 1.5 },
      { id: "screen", name: "丝网印刷", price: 2.0 },
    ],

    // 后道工艺
    finishTypes: [
      { id: "none", name: "无", price: 0 },
      { id: "lamination", name: "覆膜", price: 0.3 },
      { id: "uv", name: "UV上光", price: 0.5 },
      { id: "emboss", name: "烫金/烫银", price: 1.0 },
      { id: "die_cut", name: "模切", price: 0.8 },
    ],

    // 加急选项
    urgentOptions: [
      { id: "normal", name: "正常(7-10天)", multiplier: 1.0 },
      { id: "urgent", name: "加急(3-5天)", multiplier: 1.3 },
      { id: "super_urgent", name: "特急(1-2天)", multiplier: 1.8 },
    ],

    // 选择器索引
    materialIndex: 0,
    printTypeIndex: 0,
    finishTypeIndex: 0,
    urgentIndex: 0,

    // 下拉显示名（中文）
    materialName: "",
    printTypeName: "",
    finishTypeName: "",
    urgentLevelName: "",

    // 计算结果
    calculationResult: null,
    calculating: false,

    // 加载状态
    calculating: false,

    // 表单验证错误
    errors: {},
  },

  onLoad() {
    // 页面加载时的初始化
    console.log("Calculator page onLoad");
    console.log("Initial data:", this.data);

    this.setData({
      selectedProductType: null,
      calculationResult: null,
      calculating: false,
      materialName:
        (this.data.materials[this.data.materialIndex] || {}).name || "",
      printTypeName:
        (this.data.printTypes[this.data.printTypeIndex] || {}).name || "",
      finishTypeName:
        (this.data.finishTypes[this.data.finishTypeIndex] || {}).name || "",
      urgentLevelName:
        (this.data.urgentOptions[this.data.urgentIndex] || {}).name || "",
    });

    // 确保数据已设置
    console.log("Data after setData:", this.data);
  },

  // 产品类型点击
  onProductTypeTap(e) {
    const { item } = e.currentTarget.dataset;
    this.setData({
      selectedProductType: item,
      "calculatorParams.productTypeId": item.id,
    });
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`calculatorParams.${field}`]: value,
    });

    // 清除对应字段的错误
    if (this.data.errors[field]) {
      this.setData({
        [`errors.${field}`]: "",
      });
    }
  },

  // 数量选择器变化
  onQuantityChange(e) {
    const { type } = e.currentTarget.dataset;
    let quantity = this.data.calculatorParams.quantity;

    if (type === "minus" && quantity > 1) {
      quantity -= 100;
    } else if (type === "plus") {
      quantity += 100;
    }

    this.setData({
      "calculatorParams.quantity": Math.max(1, quantity),
    });
  },

  // 印刷颜色数变化
  onColorChange(e) {
    this.setData({
      "calculatorParams.printColors": e.detail.value,
    });
  },

  // 选择器变化（设置值 + 显示中文名）
  onPickerChange(e) {
    const { field, options } = e.currentTarget.dataset;
    const index = Number(e.detail.value);
    const selectedOption = options[index];

    const nameFieldMap = {
      material: "materialName",
      printType: "printTypeName",
      finishType: "finishTypeName",
      urgentLevel: "urgentLevelName",
    };

    this.setData({
      [`calculatorParams.${field}`]: selectedOption.id || selectedOption.name,
      [nameFieldMap[field]]: selectedOption.name,
      [`${field}Index`]: index,
    });
  },

  // 表单验证
  validateForm() {
    const { calculatorParams } = this.data;
    const errors = {};

    if (!calculatorParams.productTypeId) {
      errors.productTypeId = "请选择产品类型";
    }

    if (!calculatorParams.quantity || calculatorParams.quantity < 1) {
      errors.quantity = "请输入正确的数量";
    }

    if (!calculatorParams.width || calculatorParams.width <= 0) {
      errors.width = "请输入正确的宽度";
    }

    if (!calculatorParams.height || calculatorParams.height <= 0) {
      errors.height = "请输入正确的高度";
    }

    if (!calculatorParams.material) {
      errors.material = "请选择材质";
    }

    if (!calculatorParams.printType) {
      errors.printType = "请选择印刷方式";
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  // 计算价格
  async onCalculate() {
    if (!this.validateForm()) {
      wx.showToast({
        title: "请完善必填信息",
        icon: "none",
      });
      return;
    }

    this.setData({ calculating: true });

    try {
      // 使用模拟计算逻辑
      const params = this.data.calculatorParams;
      const basePrice = this.calculateBasePrice(params);
      const materialCost = basePrice * 0.4;
      const printCost = basePrice * 0.3;
      const finishCost = basePrice * 0.2;
      const urgentFee =
        params.urgentLevel === "urgent"
          ? basePrice * 0.3
          : params.urgentLevel === "super_urgent"
          ? basePrice * 0.8
          : 0;

      const totalPrice = basePrice + urgentFee;
      const unitPrice = totalPrice / params.quantity;

      const result = {
        unitPrice: unitPrice.toFixed(2),
        totalPrice: totalPrice.toFixed(2),
        materialCost: materialCost.toFixed(2),
        printCost: printCost.toFixed(2),
        finishCost: finishCost.toFixed(2),
        urgentFee: urgentFee.toFixed(2),
      };

      this.setData({
        calculationResult: result,
        calculating: false,
      });

      // 滚动到结果区域
      wx.pageScrollTo({
        selector: ".result-section",
        duration: 300,
      });
    } catch (error) {
      console.error("计算价格失败:", error);
      this.setData({ calculating: false });
      wx.showToast({
        title: "计算失败，请重试",
        icon: "none",
      });
    }
  },

  // 计算基础价格
  calculateBasePrice(params) {
    let basePrice = params.quantity * 2; // 基础单价2元

    // 根据尺寸调整价格
    if (params.width && params.height) {
      const area = (params.width * params.height) / 10000; // 转换为平方厘米
      basePrice += area * 0.1;
    }

    // 根据材质调整价格
    const materialMultiplier = {
      卡纸: 1.0,
      铜版纸: 1.2,
      艺术纸: 1.5,
      特种纸: 2.0,
    };
    basePrice *= materialMultiplier[params.material] || 1.0;

    // 根据印刷颜色数调整价格
    basePrice += (params.printColors - 1) * params.quantity * 0.2;

    return basePrice;
  },

  // 保存报价
  onSaveQuote() {
    if (!this.data.calculationResult) {
      wx.showToast({
        title: "请先计算价格",
        icon: "none",
      });
      return;
    }

    wx.showToast({
      title: "报价已保存",
      icon: "success",
    });
  },

  // 立即下单
  onOrderNow() {
    if (!this.data.calculationResult) {
      wx.showToast({
        title: "请先计算价格",
        icon: "none",
      });
      return;
    }

    // 跳转到下单页面，传递计算参数
    const params = encodeURIComponent(
      JSON.stringify({
        ...this.data.calculatorParams,
        calculationResult: this.data.calculationResult,
      })
    );

    wx.navigateTo({
      url: `/pages/order/order?calculatorParams=${params}`,
    });
  },

  // 重新计算
  onRecalculate() {
    this.setData({
      calculationResult: null,
    });
  },

  // 立即下单
  onOrderNow() {
    if (!this.data.calculationResult) return;

    // 将计算参数传递给下单页面
    const params = encodeURIComponent(
      JSON.stringify({
        ...this.data.calculatorParams,
        calculationResult: this.data.calculationResult,
      })
    );

    wx.navigateTo({
      url: `/pages/order/order?calculatorParams=${params}`,
    });
  },

  // 保存报价
  onSaveQuote() {
    if (!this.data.calculationResult) return;

    wx.showToast({
      title: "报价已保存",
      icon: "success",
    });
  },

  // 分享报价
  onShareQuote() {
    return {
      title: `小森包装报价 - ${this.data.selectedProductType?.name || "产品"}`,
      path: "/pages/calculator/calculator",
      imageUrl: "/images/share-calculator.jpg",
    };
  },

  // 分享
  onShareAppMessage() {
    return {
      title: "小森包装 - 在线价格计算器",
      path: "/pages/calculator/calculator",
      imageUrl: "/images/share-calculator.jpg",
    };
  },
});
