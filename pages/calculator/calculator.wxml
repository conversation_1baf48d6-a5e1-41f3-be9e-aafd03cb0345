<!-- 价格计算器页面 -->
<view class="page-container">
  <!-- 使用提示 -->
  <view class="tips-section" wx:if="{{!selectedProductType}}">
    <view class="tips-content">
      <text class="tips-icon">💡</text>
      <text class="tips-text">请先选择产品类型，然后设置规格参数进行价格计算</text>
    </view>
  </view>

  <!-- 产品类型选择 -->
  <view class="product-types card">
    <view class="section-title">选择产品类型</view>
    <view class="type-grid">
      <view
        class="type-item {{selectedProductType && selectedProductType.id === item.id ? 'selected' : ''}}"
        wx:for="{{productTypes}}"
        wx:key="id"
        bindtap="onProductTypeTap"
        data-item="{{item}}"
      >
        <image class="type-icon" src="{{item.icon}}" mode="aspectFit" />
        <text class="type-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 规格参数 -->
  <view class="params-section card" wx:if="{{selectedProductType}}">
    <view class="section-title">设置规格参数</view>

    <!-- 数量 -->
    <view class="param-item">
      <text class="param-label">数量</text>
      <view class="quantity-control">
        <button class="quantity-btn" bindtap="onQuantityChange" data-type="minus">-</button>
        <input class="quantity-input" type="number" value="{{calculatorParams.quantity}}" bindinput="onInputChange" data-field="quantity" />
        <button class="quantity-btn" bindtap="onQuantityChange" data-type="plus">+</button>
      </view>
    </view>

    <!-- 尺寸 -->
    <view class="param-item">
      <text class="param-label">尺寸 (mm)</text>
      <view class="size-inputs">
        <input class="size-input" placeholder="宽度" type="number" value="{{calculatorParams.width}}" bindinput="onInputChange" data-field="width" />
        <text class="size-separator">×</text>
        <input class="size-input" placeholder="高度" type="number" value="{{calculatorParams.height}}" bindinput="onInputChange" data-field="height" />
        <text class="size-separator">×</text>
        <input class="size-input" placeholder="深度" type="number" value="{{calculatorParams.depth}}" bindinput="onInputChange" data-field="depth" />
      </view>
    </view>

    <!-- 材质选择 -->
    <view class="param-item">
      <text class="param-label">材质</text>
      <picker range="{{materials}}" range-key="name" value="{{materialIndex}}" bindchange="onPickerChange" data-field="material" data-options="{{materials}}">
        <view class="picker-display">
          <text class="picker-text">{{materialName || '请选择材质'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 印刷方式 -->
    <view class="param-item">
      <text class="param-label">印刷方式</text>
      <picker range="{{printTypes}}" range-key="name" value="{{printTypeIndex}}" bindchange="onPickerChange" data-field="printType" data-options="{{printTypes}}">
        <view class="picker-display">
          <text class="picker-text">{{printTypeName || '请选择印刷方式'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 印刷颜色数 -->
    <view class="param-item">
      <text class="param-label">印刷颜色数</text>
      <slider min="1" max="8" value="{{calculatorParams.printColors}}" bindchange="onColorChange" show-value />
    </view>

    <!-- 后道工艺 -->
    <view class="param-item">
      <text class="param-label">后道工艺</text>
      <picker range="{{finishTypes}}" range-key="name" value="{{finishTypeIndex}}" bindchange="onPickerChange" data-field="finishType" data-options="{{finishTypes}}">
        <view class="picker-display">
          <text class="picker-text">{{finishTypeName || '请选择后道工艺'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <!-- 加急程度 -->
    <view class="param-item">
      <text class="param-label">加急程度</text>
      <picker range="{{urgentOptions}}" range-key="name" value="{{urgentIndex}}" bindchange="onPickerChange" data-field="urgentLevel" data-options="{{urgentOptions}}">
        <view class="picker-display">
          <text class="picker-text">{{urgentLevelName || '请选择加急程度'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>
  </view>

  <!-- 计算按钮 -->
  <view class="calculate-section" wx:if="{{selectedProductType}}">
    <button class="calculate-btn {{calculating ? 'calculating' : ''}}" bindtap="onCalculate" disabled="{{calculating}}">
      {{calculating ? '计算中...' : '计算价格'}}
    </button>
  </view>

  <!-- 计算结果 -->
  <view class="result-section card" wx:if="{{calculationResult}}">
    <view class="section-title">报价结果</view>

    <view class="result-summary">
      <view class="result-item">
        <text class="result-label">单价</text>
        <text class="result-value">¥{{calculationResult.unitPrice}}</text>
      </view>
      <view class="result-item">
        <text class="result-label">总价</text>
        <text class="result-value total">¥{{calculationResult.totalPrice}}</text>
      </view>
    </view>

    <view class="result-details">
      <view class="detail-item">
        <text class="detail-label">材料成本</text>
        <text class="detail-value">¥{{calculationResult.materialCost}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">印刷成本</text>
        <text class="detail-value">¥{{calculationResult.printCost}}</text>
      </view>
      <view class="detail-item">
        <text class="detail-label">后道工艺</text>
        <text class="detail-value">¥{{calculationResult.finishCost}}</text>
      </view>
      <view class="detail-item" wx:if="{{calculationResult.urgentFee > 0}}">
        <text class="detail-label">加急费用</text>
        <text class="detail-value">¥{{calculationResult.urgentFee}}</text>
      </view>
    </view>

    <view class="result-actions">
      <button class="action-btn secondary" bindtap="onSaveQuote">保存报价</button>
      <button class="action-btn primary" bindtap="onOrderNow">立即下单</button>
    </view>
  </view>
</view>