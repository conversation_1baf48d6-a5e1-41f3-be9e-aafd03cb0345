/* 价格计算器页面样式 */

.page-container {
  background-color: #f8fafc;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 使用提示 */
.tips-section {
  margin: 24rpx 32rpx;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  border-left: 6rpx solid var(--primary-color);
}

.tips-content {
  display: flex;
  align-items: center;
}

.tips-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.tips-text {
  font-size: 26rpx;
  color: var(--primary-color);
  line-height: 1.5;
}

/* 产品类型选择 */
.product-types {
  margin: 24rpx 32rpx;
  background-color: var(--white);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 32rpx;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: 2rpx;
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background-color: #f8fafc;
  border: 3rpx solid transparent;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.type-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.type-item.selected::before {
  opacity: 1;
}

.type-item.selected {
  border-color: var(--primary-color);
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(30, 58, 138, 0.2);
}

.type-item.selected .type-name,
.type-item.selected .type-icon {
  position: relative;
  z-index: 1;
  color: var(--white);
}

.type-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
}

.type-name {
  font-size: 24rpx;
  color: var(--text-primary);
  text-align: center;
}

/* 参数设置区域 */
.params-section {
  margin: 24rpx 32rpx;
  background-color: var(--white);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.param-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 0;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
}

.param-item:last-child {
  border-bottom: none;
}

.param-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), #3b82f6);
  border-radius: 3rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.param-item:hover::before {
  opacity: 1;
}

.param-label {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 600;
  min-width: 140rpx;
  position: relative;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  border: 2rpx solid var(--border-color);
  border-radius: 8rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: var(--gray-100);
  border: none;
  font-size: 28rpx;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background-color: var(--white);
}

/* 尺寸输入 */
.size-inputs {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.size-input {
  width: 100rpx;
  height: 60rpx;
  padding: 0 16rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 8rpx;
  font-size: 26rpx;
  text-align: center;
}

.size-separator {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 选择器样式 */
.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 200rpx;
  padding: 16rpx 20rpx;
  background-color: var(--gray-50);
  border-radius: 8rpx;
}

.picker-text {
  font-size: 26rpx;
  color: var(--text-primary);
}

.picker-arrow {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 计算按钮 */
.calculate-section {
  margin: 32rpx;
}

.calculate-btn {
  width: 100%;
  height: 88rpx;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  border-radius: 12rpx;
  transition: background-color 0.3s ease;
}

.calculate-btn:disabled {
  background-color: var(--gray-300);
  color: var(--text-secondary);
}

.calculate-btn:not(:disabled):active {
  background-color: #1e40af;
  transform: scale(0.98);
}

.calculate-btn.calculating {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
}

.calculate-btn.calculating::after {
  content: "";
  width: 20rpx;
  height: 20rpx;
  border: 3rpx solid transparent;
  border-top: 3rpx solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 16rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 计算结果区域 */
.result-section {
  margin: 24rpx 32rpx;
}

.result-summary {
  background-color: var(--gray-50);
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-label {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.result-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--secondary-color);
}

.result-value.total {
  font-size: 40rpx;
  color: var(--primary-color);
}

/* 详细费用 */
.result-details {
  margin-bottom: 32rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 26rpx;
  color: var(--text-primary);
}

/* 操作按钮 */
.result-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.action-btn.primary:active {
  background-color: #1e40af;
}

.action-btn.secondary {
  background-color: var(--gray-100);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
}

.action-btn.secondary:active {
  background-color: var(--gray-200);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .type-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .size-inputs {
    flex-direction: column;
    gap: 12rpx;
  }

  .size-input {
    width: 100%;
  }

  .param-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }

  .picker-display {
    width: 100%;
  }
}
