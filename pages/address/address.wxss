/* 地址列表 */
.page-container { background: #f8fafc; min-height: 100vh; padding-bottom: 120rpx; }
.card { background: #fff; margin: 24rpx 32rpx; border-radius: 16rpx; box-shadow: 0 4rpx 12rpx rgba(0,0,0,.05); padding: 24rpx; }
.address-item { display: flex; justify-content: space-between; align-items: center; padding: 24rpx 0; border-bottom: 1px solid var(--border-color); }
.address-main { flex: 1; }
.addr-name { font-size: 28rpx; font-weight: 600; color: var(--text-primary); margin-right: 12rpx; }
.addr-phone { font-size: 26rpx; color: var(--text-secondary); }
.addr-detail { font-size: 26rpx; color: var(--text-primary); margin-top: 8rpx; }
.tags { display: inline-flex; gap: 8rpx; margin-left: 8rpx; }
.tag { padding: 4rpx 12rpx; border-radius: 20rpx; font-size: 20rpx; background: var(--gray-100); color: var(--text-secondary); }
.actions { display: flex; gap: 16rpx; }
.link { font-size: 26rpx; color: var(--primary-color); }
.toolbar { position: fixed; left: 0; right: 0; bottom: 0; padding: 24rpx 32rpx; background: #fff; border-top: 1px solid var(--border-color); }
.primary { width: 100%; height: 88rpx; border-radius: 12rpx; background: var(--primary-color); color:#fff; font-size: 32rpx; font-weight: 700; }

