// 地址管理页面
const { get, post, del } = require("../../utils/request");

Page({
  data: {
    // 地址列表
    addressList: [],

    // 页面模式：list(列表) | select(选择)
    mode: "list",

    // 加载状态
    loading: false,
  },

  onLoad(options) {
    this.setData({
      mode: options.mode || "list",
    });

    if (this.data.mode === "select") {
      wx.setNavigationBarTitle({
        title: "选择收货地址",
      });
    }

    this.loadAddressList();
  },

  onShow() {
    // 从添加/编辑页面返回时刷新列表
    this.loadAddressList();
  },

  // 加载地址列表
  async loadAddressList() {
    try {
      this.setData({ loading: true });
      const addressList = await get("/api/user/addresses");
      this.setData({
        addressList,
        loading: false,
      });
    } catch (error) {
      console.error("加载地址列表失败:", error);
      this.setData({ loading: false });
    }
  },

  // 添加地址
  onAddAddress() {
    wx.navigateTo({
      url: "/pages/address-edit/address-edit",
    });
  },

  // 编辑地址
  onEditAddress(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${id}`,
    });
  },

  // 删除地址
  async onDeleteAddress(e) {
    const { id, index } = e.currentTarget.dataset;

    const confirmed = await new Promise((resolve) => {
      wx.showModal({
        title: "确认删除",
        content: "确定要删除这个地址吗？",
        success: (res) => resolve(res.confirm),
      });
    });

    if (!confirmed) return;

    try {
      await del(`/api/user/addresses/${id}`);

      const addressList = [...this.data.addressList];
      addressList.splice(index, 1);

      this.setData({ addressList });

      wx.showToast({
        title: "删除成功",
        icon: "success",
      });
    } catch (error) {
      console.error("删除地址失败:", error);
      wx.showToast({
        title: "删除失败",
        icon: "none",
      });
    }
  },

  // 设置默认地址
  async onSetDefault(e) {
    const { id } = e.currentTarget.dataset;

    try {
      await post(`/api/user/addresses/${id}/set-default`);

      // 更新本地数据
      const addressList = this.data.addressList.map((addr) => ({
        ...addr,
        isDefault: addr.id === id,
      }));

      this.setData({ addressList });

      wx.showToast({
        title: "设置成功",
        icon: "success",
      });
    } catch (error) {
      console.error("设置默认地址失败:", error);
      wx.showToast({
        title: "设置失败",
        icon: "none",
      });
    }
  },

  // 选择地址（选择模式）
  onSelectAddress(e) {
    if (this.data.mode !== "select") return;

    const { address } = e.currentTarget.dataset;

    // 通过事件通知上级页面
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];

    if (prevPage && prevPage.onAddressSelected) {
      prevPage.onAddressSelected(address);
    }

    wx.navigateBack();
  },
});
