<!-- 地址管理/选择 -->
<view class="page-container">
  <view class="card">
    <block wx:if="{{addressList.length}}">
      <view class="address-item" wx:for="{{addressList}}" wx:key="id">
        <view class="address-main" bindtap="onSelectAddress" data-address="{{item}}">
          <view>
            <text class="addr-name">{{item.name}}</text>
            <text class="addr-phone">{{item.phone}}</text>
            <view class="tags">
              <text class="tag" wx:if="{{item.isDefault}}">默认</text>
              <text class="tag" wx:if="{{item.tag}}">{{item.tag}}</text>
            </view>
          </view>
          <view class="addr-detail">{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</view>
        </view>
        <view class="actions">
          <text class="link" bindtap="onEditAddress" data-id="{{item.id}}">编辑</text>
          <text class="link" bindtap="onSetDefault" data-id="{{item.id}}" wx:if="{{!item.isDefault}}">设为默认</text>
          <text class="link" bindtap="onDeleteAddress" data-id="{{item.id}}" data-index="{{index}}">删除</text>
        </view>
      </view>
    </block>
    <block wx:else>
      <view style="text-align:center; color: var(--text-secondary); padding: 48rpx 0;">暂无地址</view>
    </block>
  </view>

  <view class="toolbar">
    <button class="primary" bindtap="onAddAddress">新增地址</button>
  </view>
</view>