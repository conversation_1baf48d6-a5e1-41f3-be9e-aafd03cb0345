// 登录页面
const app = getApp();
const { post } = require("../../utils/request");

Page({
  data: {
    // 登录方式
    loginType: "phone", // phone | wechat

    // 手机号登录
    phoneNumber: "",
    verifyCode: "",

    // 验证码相关
    codeText: "获取验证码",
    codeDisabled: false,
    countdown: 0,

    // 加载状态
    loading: false,

    // 协议同意
    agreeProtocol: false,
  },

  onLoad(options) {
    // 检查是否已登录
    if (app.globalData.isLogin) {
      wx.navigateBack();
    }
  },

  // 切换登录方式
  onLoginTypeChange(e) {
    this.setData({
      loginType: e.currentTarget.dataset.type,
    });
  },

  // 手机号输入
  onPhoneInput(e) {
    this.setData({
      phoneNumber: e.detail.value,
    });
  },

  // 验证码输入
  onCodeInput(e) {
    this.setData({
      verifyCode: e.detail.value,
    });
  },

  // 获取验证码
  async onGetCode() {
    if (!this.data.phoneNumber) {
      wx.showToast({
        title: "请输入手机号",
        icon: "none",
      });
      return;
    }

    if (!/^1[3-9]\d{9}$/.test(this.data.phoneNumber)) {
      wx.showToast({
        title: "手机号格式不正确",
        icon: "none",
      });
      return;
    }

    try {
      await post("/api/auth/send-code", {
        phone: this.data.phoneNumber,
      });

      wx.showToast({
        title: "验证码已发送",
        icon: "success",
      });

      this.startCountdown();
    } catch (error) {
      console.error("发送验证码失败:", error);
    }
  },

  // 开始倒计时
  startCountdown() {
    this.setData({
      countdown: 60,
      codeDisabled: true,
    });

    const timer = setInterval(() => {
      const countdown = this.data.countdown - 1;

      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({
          countdown: 0,
          codeDisabled: false,
          codeText: "获取验证码",
        });
      } else {
        this.setData({
          countdown,
          codeText: `${countdown}s后重新获取`,
        });
      }
    }, 1000);
  },

  // 协议同意状态切换
  onProtocolChange(e) {
    this.setData({
      agreeProtocol: e.detail.value,
    });
  },

  // 手机号登录
  async onPhoneLogin() {
    if (!this.validatePhoneLogin()) {
      return;
    }

    try {
      this.setData({ loading: true });

      const result = await post("/api/auth/login", {
        phone: this.data.phoneNumber,
        code: this.data.verifyCode,
      });

      // 保存登录状态
      app.globalData.isLogin = true;
      app.globalData.token = result.token;
      app.globalData.userInfo = result.userInfo;
      wx.setStorageSync("token", result.token);

      wx.showToast({
        title: "登录成功",
        icon: "success",
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error("登录失败:", error);
      wx.showToast({
        title: "登录失败",
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 微信登录
  onWechatLogin() {
    wx.getUserProfile({
      desc: "用于完善用户资料",
      success: async (res) => {
        try {
          this.setData({ loading: true });

          // 获取登录凭证
          const loginRes = await new Promise((resolve, reject) => {
            wx.login({
              success: resolve,
              fail: reject,
            });
          });

          const result = await post("/api/auth/wechat-login", {
            code: loginRes.code,
            userInfo: res.userInfo,
          });

          // 保存登录状态
          app.globalData.isLogin = true;
          app.globalData.token = result.token;
          app.globalData.userInfo = result.userInfo;
          wx.setStorageSync("token", result.token);

          wx.showToast({
            title: "登录成功",
            icon: "success",
          });

          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } catch (error) {
          console.error("微信登录失败:", error);
          wx.showToast({
            title: "登录失败",
            icon: "none",
          });
        } finally {
          this.setData({ loading: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: "需要授权才能登录",
          icon: "none",
        });
      },
    });
  },

  // 验证手机号登录
  validatePhoneLogin() {
    if (!this.data.phoneNumber) {
      wx.showToast({
        title: "请输入手机号",
        icon: "none",
      });
      return false;
    }

    if (!this.data.verifyCode) {
      wx.showToast({
        title: "请输入验证码",
        icon: "none",
      });
      return false;
    }

    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: "请同意用户协议",
        icon: "none",
      });
      return false;
    }

    return true;
  },
});
