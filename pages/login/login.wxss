/* 登录页样式 */
.login-page {
  padding-bottom: 80rpx;
}

.login-hero {
  padding: 60rpx 32rpx 24rpx;
  text-align: center;
}

.login-title {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.login-subtitle {
  margin-top: 8rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
}

.login-tabs {
  display: flex;
  margin: 0 32rpx 16rpx;
  background-color: var(--gray-100);
  border-radius: 12rpx;
  overflow: hidden;
}

.login-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: var(--text-secondary);
}

.login-tab.active {
  background-color: var(--white);
  color: var(--primary-color);
  font-weight: 600;
}

.card {
  margin: 16rpx 32rpx;
  background-color: var(--white);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
  padding: 24rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: var(--text-secondary);
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.code-row {
  display: flex;
  gap: 12rpx;
}

.code-btn {
  flex: 0 0 220rpx;
  height: 80rpx;
  border: none;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 8rpx;
  font-size: 26rpx;
}

.protocol-row {
  margin: 12rpx 0 20rpx;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.protocol-label .link {
  color: var(--primary-color);
}

.primary-btn {
  width: 100%;
  height: 88rpx;
  border: none;
  border-radius: 12rpx;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 30rpx;
  font-weight: bold;
}

.primary-btn.wechat {
  background: linear-gradient(135deg, #059669, #10b981);
}
