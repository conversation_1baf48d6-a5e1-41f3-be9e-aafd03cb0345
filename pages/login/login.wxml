<!-- 登录页面 -->
<view class="page-container login-page">
  <!-- 标题区 -->
  <view class="login-hero">
    <text class="login-title">登录济南小森</text>
    <text class="login-subtitle">欢迎回来，请选择登录方式</text>
  </view>

  <!-- 登录方式切换 -->
  <view class="login-tabs">
    <view class="login-tab {{loginType === 'phone' ? 'active' : ''}}" data-type="phone" bindtap="onLoginTypeChange">手机号登录</view>
    <view class="login-tab {{loginType === 'wechat' ? 'active' : ''}}" data-type="wechat" bindtap="onLoginTypeChange">微信登录</view>
  </view>

  <!-- 手机号登录 -->
  <view wx:if="{{loginType === 'phone'}}" class="card">
    <view class="form-item">
      <text class="form-label">手机号</text>
      <input class="form-input" type="number" maxlength="11" placeholder="请输入手机号" value="{{phoneNumber}}" bindinput="onPhoneInput" />
    </view>

    <view class="form-item">
      <text class="form-label">验证码</text>
      <view class="code-row">
        <input class="form-input" placeholder="请输入验证码" value="{{verifyCode}}" bindinput="onCodeInput" />
        <button class="code-btn" bindtap="onGetCode" disabled="{{codeDisabled}}">{{codeText}}</button>
      </view>
    </view>

    <view class="protocol-row">
      <checkbox-group bindchange="onProtocolChange">
        <label class="protocol-label">
          <checkbox value="agree" checked="{{agreeProtocol}}"/> 我已阅读并同意
          <text class="link">《用户协议》</text>和<text class="link">《隐私政策》</text>
        </label>
      </checkbox-group>
    </view>

    <button class="primary-btn" bindtap="onPhoneLogin" loading="{{loading}}">登录</button>
  </view>

  <!-- 微信登录 -->
  <view wx:elif="{{loginType === 'wechat'}}" class="card">
    <button class="primary-btn wechat" open-type="getUserProfile" bindtap="onWechatLogin" loading="{{loading}}">微信授权登录</button>
  </view>
</view>