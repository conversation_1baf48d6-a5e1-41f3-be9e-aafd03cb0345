/* 产品详情页样式 */

/* 图片区域 */
.image-section {
  position: relative;
  height: 620rpx;
  background-color: var(--white);
}

.product-swiper {
  width: 100%;
  height: 100%;
}

.product-image {
  width: 100%;
  height: 100%;
}

.image-indicator {
  position: absolute;
  bottom: 32rpx;
  right: 32rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--white);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.favorite-btn {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.favorite-btn.favorited {
  color: var(--error-color);
  background-color: rgba(255, 255, 255, 1);
}

/* 产品信息区域 */
.product-info {
  margin: 20rpx 24rpx;
}

.product-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.product-name {
  flex: 1;
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  line-height: 1.4;
}

.product-tags {
  display: flex;
  gap: 12rpx;
  margin-left: 16rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background-color: var(--secondary-color);
  color: var(--white);
  font-size: 20rpx;
  border-radius: 20rpx;
}

.product-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 32rpx;
}

/* 价格区域 */
.price-section {
  border-top: 1px solid var(--border-color);
  padding-top: 24rpx;
}

.price-main {
  display: flex;
  align-items: baseline;
  margin-bottom: 16rpx;
}

.price-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-right: 16rpx;
}

.price-value {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--secondary-color);
}

.price-unit {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-left: 8rpx;
}

.price-total {
  display: flex;
  align-items: baseline;
}

.total-label {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-right: 16rpx;
}

.total-value {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
}

/* 规格选择区域 */
.specs-section {
  margin: 24rpx 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 24rpx;
}

.spec-group {
  margin-bottom: 32rpx;
}

.spec-name {
  display: block;
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.spec-option {
  padding: 16rpx 32rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 12rpx;
  font-size: 26rpx;
  color: var(--text-primary);
  background-color: var(--white);
  transition: all 0.3s ease;
}

.spec-option.selected {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  color: var(--white);
}

/* 数量选择区域 */
.quantity-section {
  margin: 24rpx 32rpx;
}

.quantity-control {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.quantity-input {
  display: flex;
  align-items: center;
  border: 2rpx solid var(--border-color);
  border-radius: 12rpx;
  overflow: hidden;
}

.quantity-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: var(--gray-100);
  border: none;
  font-size: 32rpx;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:disabled {
  opacity: 0.5;
}

.quantity-btn.minus {
  border-right: 2rpx solid var(--border-color);
}

.quantity-btn.plus {
  border-left: 2rpx solid var(--border-color);
}

.quantity-value {
  width: 120rpx;
  height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background-color: var(--white);
}

.quantity-note {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 详情区域 */
.detail-section {
  margin: 24rpx 32rpx;
}

.detail-content {
  font-size: 28rpx;
  line-height: 1.8;
  color: var(--text-primary);
}

/* 评价区域 */
.reviews-section {
  margin: 24rpx 32rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.more-link {
  font-size: 24rpx;
  color: var(--primary-color);
}

.review-summary {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background-color: var(--gray-50);
  border-radius: 12rpx;
}

.rating-score {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--secondary-color);
  margin-right: 24rpx;
}

.review-item {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
}

.review-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.user-name {
  font-size: 26rpx;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.review-date {
  font-size: 20rpx;
  color: var(--text-secondary);
}

.review-content {
  font-size: 26rpx;
  color: var(--text-primary);
  line-height: 1.6;
}

/* 相关产品区域 */
.related-section {
  margin: 24rpx 32rpx;
}

.related-scroll {
  white-space: nowrap;
}

.related-list {
  display: flex;
  gap: 24rpx;
}

.related-item {
  flex-shrink: 0;
  width: 200rpx;
  text-align: center;
}

.related-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.related-name {
  display: block;
  font-size: 24rpx;
  color: var(--text-primary);
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  font-size: 26rpx;
  font-weight: bold;
  color: var(--secondary-color);
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: var(--white);
  border-top: 1px solid var(--border-color);
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  padding: 16rpx;
  margin-right: 16rpx;
}

.contact-btn,
.cart-btn {
  flex: 0 0 120rpx;
  background-color: var(--gray-100);
  color: var(--text-primary);
}

.order-btn {
  flex: 1;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 28rpx;
  font-weight: bold;
}

.action-btn .iconfont {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: var(--text-secondary);
}

/* 底部间距 */
.page-container {
  padding-bottom: 200rpx;
}
