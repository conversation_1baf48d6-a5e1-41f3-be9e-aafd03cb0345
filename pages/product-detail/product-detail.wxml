<!-- 产品详情页 -->
<view class="page-container">
  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 产品内容 -->
  <view wx:else>
    <!-- 产品图片轮播 -->
    <view class="image-section">
      <swiper
        class="product-swiper"
        indicator-dots="{{true}}"
        autoplay="{{false}}"
        bindchange="onSwiperChange"
      >
        <swiper-item wx:for="{{images.length ? images : (product && product.image ? [product.image] : [])}}" wx:key="index">
          <image
            class="product-image"
            src="{{item}}"
            mode="aspectFill"
            bindtap="onImageTap"
            data-index="{{index}}"
          />
        </swiper-item>
      </swiper>

      <!-- 图片指示器 -->
      <view class="image-indicator">
        {{currentImageIndex + 1}} / {{images.length}}
      </view>

      <!-- 收藏按钮 -->
      <view class="favorite-btn {{isFavorited ? 'favorited' : ''}}" bindtap="onFavoriteTap">
        <text class="iconfont icon-heart"></text>
      </view>
    </view>

    <!-- 产品基本信息 -->
    <view class="product-info card">
      <view class="product-header">
        <text class="product-name">{{product.name}}</text>
        <view class="product-tags">
          <text class="tag" wx:if="{{product.isHot}}">热销</text>
          <text class="tag" wx:if="{{product.isNew}}">新品</text>
        </view>
      </view>

      <text class="product-desc">{{product.description}}</text>

      <!-- 价格信息 -->
      <view class="price-section">
        <view class="price-main">
          <text class="price-label">单价</text>
          <text class="price-value">¥{{priceInfo.unitPrice}}</text>
          <text class="price-unit">/{{product.unit || '件'}}</text>
        </view>
        <view class="price-total" wx:if="{{quantity > 1}}">
          <text class="total-label">总价</text>
          <text class="total-value">¥{{priceInfo.totalPrice}}</text>
        </view>
      </view>
    </view>

    <!-- 规格选择 -->
    <view class="specs-section card" wx:if="{{specifications.length > 0}}">
      <view class="section-title">选择规格</view>
      <view class="spec-group" wx:for="{{specifications}}" wx:key="id">
        <text class="spec-name">{{item.name}}</text>
        <view class="spec-options">
          <view
            class="spec-option {{selectedSpecs[item.id] === option.id ? 'selected' : ''}}"
            wx:for="{{item.options}}"
            wx:for-item="option"
            wx:key="id"
            bindtap="onSpecTap"
            data-spec-id="{{item.id}}"
            data-option-id="{{option.id}}"
          >
            {{option.name}}
          </view>
        </view>
      </view>
    </view>

    <!-- 数量选择 -->
    <view class="quantity-section card">
      <view class="section-title">选择数量</view>
      <view class="quantity-control">
        <view class="quantity-input">
          <button
            class="quantity-btn minus"
            bindtap="onQuantityChange"
            data-type="minus"
            disabled="{{quantity <= minQuantity}}"
          >
            -
          </button>
          <input
            class="quantity-value"
            type="number"
            value="{{quantity}}"
            bindinput="onQuantityInput"
          />
          <button
            class="quantity-btn plus"
            bindtap="onQuantityChange"
            data-type="plus"
          >
            +
          </button>
        </view>
        <text class="quantity-note">最小起订量：{{minQuantity}}{{product.unit || '件'}}</text>
      </view>
    </view>

    <!-- 产品详情 -->
    <view class="detail-section card">
      <view class="section-title">产品详情</view>
      <view class="detail-content">
        <rich-text nodes="{{product.detailContent}}"></rich-text>
      </view>
    </view>

    <!-- 用户评价 -->
    <view class="reviews-section card" wx:if="{{reviews.totalCount > 0}}">
      <view class="section-header">
        <text class="section-title">用户评价 ({{reviews.totalCount}})</text>
        <text class="more-link" bindtap="onMoreReviews">查看全部</text>
      </view>

      <view class="review-summary">
        <text class="rating-score">{{reviews.averageRating}}</text>
        <view class="rating-stars">
          <!-- 星级评分显示 -->
        </view>
      </view>

      <view class="review-list">
        <view class="review-item" wx:for="{{reviews.list}}" wx:key="id">
          <view class="review-header">
            <image class="user-avatar" src="{{item.userAvatar}}" />
            <view class="user-info">
              <text class="user-name">{{item.userName}}</text>
              <text class="review-date">{{item.createTime}}</text>
            </view>
          </view>
          <text class="review-content">{{item.content}}</text>
        </view>
      </view>
    </view>

    <!-- 相关产品 -->
    <view class="related-section card" wx:if="{{relatedProducts.length > 0}}">
      <view class="section-title">相关产品</view>
      <scroll-view class="related-scroll" scroll-x="{{true}}">
        <view class="related-list">
          <view
            class="related-item"
            wx:for="{{relatedProducts}}"
            wx:key="id"
            bindtap="onRelatedProductTap"
            data-id="{{item.id}}"
          >
            <image class="related-image" src="{{item.image}}" mode="aspectFill" />
            <text class="related-name">{{item.name}}</text>
            <text class="related-price">¥{{item.price}}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{!loading}}">
    <button class="action-btn contact-btn" bindtap="onContactService">
      <text class="iconfont icon-service"></text>
      <text>客服</text>
    </button>
    <button class="action-btn cart-btn" bindtap="onAddToCart">
      <text class="iconfont icon-cart"></text>
      <text>加购物车</text>
    </button>
    <button class="action-btn order-btn" bindtap="onOrderNow">
      立即下单
    </button>
  </view>
</view>