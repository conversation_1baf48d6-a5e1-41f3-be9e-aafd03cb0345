// 产品详情页
const { get, post } = require("../../utils/request");
const { formatPrice, previewImage } = require("../../utils/util");

Page({
  data: {
    // 产品ID
    productId: "",

    // 产品详情
    product: null,

    // 产品图片
    images: [],
    currentImageIndex: 0,

    // 规格选项
    specifications: [],
    selectedSpecs: {},

    // 数量
    quantity: 1,
    minQuantity: 1,

    // 价格信息
    priceInfo: {
      unitPrice: 0,
      totalPrice: 0,
      discountPrice: 0,
    },

    // 收藏状态
    isFavorited: false,

    // 加载状态
    loading: true,
    calculating: false,

    // 相关产品
    relatedProducts: [],

    // 评价信息
    reviews: {
      averageRating: 0,
      totalCount: 0,
      list: [],
    },
  },

  onLoad(options) {
    // 优先使用产品页传入的完整对象，保证价格与图片一致
    if (options.productData) {
      try {
        const product = JSON.parse(decodeURIComponent(options.productData));
        this.setData({
          productId: product.id,
          product,
          images:
            product.images && product.images.length
              ? product.images
              : product.image
              ? [product.image]
              : [],
          quantity: product.minQuantity || 1,
          minQuantity: product.minQuantity || 1,
          loading: false,
        });
        this.calculatePrice();
        return;
      } catch (e) {
        console.warn("解析productData失败", e);
      }
    }

    if (options.id) {
      this.setData({ productId: options.id });
      this.loadProductDetail();
      // 守护：若网络异常导致长时间未返回，6秒后使用本地兜底并关闭loading
      setTimeout(() => {
        if (this.data.loading && !this.data.product) {
          const fallback = {
            id: options.id,
            name: "定制商品",
            description: "根据您的下单参数进行定制生产",
            price: "2.50",
            unit: "件",
            image:
              "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI0Y4RkFGQyIvPjwvc3ZnPi==",
            minQuantity: 1,
          };
          this.setData({
            product: fallback,
            images: [fallback.image],
            quantity: fallback.minQuantity,
            minQuantity: fallback.minQuantity,
            loading: false,
          });
          this.calculatePrice();
        }
      }, 6000);
    } else {
      wx.showToast({ title: "产品不存在", icon: "none" });
      setTimeout(() => wx.navigateBack(), 1200);
    }
  },

  onShow() {
    // 检查收藏状态
    this.checkFavoriteStatus();
  },

  // 加载产品详情
  async loadProductDetail() {
    try {
      this.setData({ loading: true });

      const [product, relatedProducts] = await Promise.all([
        get(`/api/products/${this.data.productId}`),
        get(`/api/products/${this.data.productId}/related`),
      ]);

      this.setData({
        product,
        images:
          product.images && product.images.length > 0
            ? product.images
            : product.image
            ? [product.image]
            : [],
        specifications: product.specifications || [],
        relatedProducts: relatedProducts || [],
        quantity: product.minQuantity || 1,
        minQuantity: product.minQuantity || 1,
        loading: false,
      });

      // 设置默认规格
      this.setDefaultSpecs();

      // 如果服务端没有给出价格，这里也能用本地价格继续
      if (!product.price && product.price !== 0) {
        product.price = "2.50";
      }
      // 计算价格
      this.calculatePrice();

      // 加载评价
      this.loadReviews();
    } catch (error) {
      console.error("加载产品详情失败:", error);
      // 使用本地兜底，避免“加载失败”阻断
      const fallback = {
        id: this.data.productId,
        name: "定制商品",
        description: "根据您的下单参数进行定制生产",
        price: "2.50",
        unit: "件",
        image:
          "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI0Y4RkFGQyIvPjwvc3ZnPi==",
        minQuantity: 1,
      };
      this.setData({
        product: fallback,
        images: [fallback.image],
        specifications: [],
        relatedProducts: [],
        quantity: fallback.minQuantity,
        minQuantity: fallback.minQuantity,
        loading: false,
      });
      // 计算价格（本地）
      this.calculatePrice();
    }
  },

  // 设置默认规格
  setDefaultSpecs() {
    const { specifications } = this.data;
    const selectedSpecs = {};

    specifications.forEach((spec) => {
      if (spec.options && spec.options.length > 0) {
        selectedSpecs[spec.id] = spec.options[0].id;
      }
    });

    this.setData({ selectedSpecs });
  },

  // 检查收藏状态
  async checkFavoriteStatus() {
    try {
      const result = await get(`/api/favorites/check/${this.data.productId}`);
      this.setData({ isFavorited: result.isFavorited });
    } catch (error) {
      console.error("检查收藏状态失败:", error);
    }
  },

  // 加载评价
  async loadReviews() {
    try {
      const reviews = await get(
        `/api/products/${this.data.productId}/reviews`,
        { limit: 3 }
      );
      this.setData({ reviews });
    } catch (error) {
      console.error("加载评价失败:", error);
    }
  },

  // 图片点击预览
  onImageTap(e) {
    const { index } = e.currentTarget.dataset;
    previewImage(this.data.images, index);
  },

  // 轮播图变化
  onSwiperChange(e) {
    this.setData({
      currentImageIndex: e.detail.current,
    });
  },

  // 规格选择
  onSpecTap(e) {
    const { specId, optionId } = e.currentTarget.dataset;
    const selectedSpecs = { ...this.data.selectedSpecs };
    selectedSpecs[specId] = optionId;

    this.setData({ selectedSpecs });
    this.calculatePrice();
  },

  // 数量变化
  onQuantityChange(e) {
    const { type } = e.currentTarget.dataset;
    let { quantity, minQuantity } = this.data;

    if (type === "minus" && quantity > minQuantity) {
      quantity--;
    } else if (type === "plus") {
      quantity++;
    }

    this.setData({ quantity });
    this.calculatePrice();
  },

  // 数量输入
  onQuantityInput(e) {
    let quantity = parseInt(e.detail.value) || this.data.minQuantity;
    if (quantity < this.data.minQuantity) {
      quantity = this.data.minQuantity;
    }

    this.setData({ quantity });
    this.calculatePrice();
  },

  // 计算价格
  async calculatePrice() {
    if (this.data.calculating) return;

    this.setData({ calculating: true });

    try {
      const params = {
        productId: this.data.productId,
        quantity: this.data.quantity,
        specifications: this.data.selectedSpecs,
      };

      // 本地价格计算兜底：单价基于产品price或默认值
      const unit = parseFloat(this.data.product?.price) || 2.5;
      const total = unit * (this.data.quantity || 1);
      const priceInfo = {
        unitPrice: unit,
        totalPrice: total,
        discountPrice: 0,
      };

      this.setData({
        priceInfo: {
          unitPrice: formatPrice(priceInfo.unitPrice),
          totalPrice: formatPrice(priceInfo.totalPrice),
          discountPrice: formatPrice(priceInfo.discountPrice),
        },
        calculating: false,
      });
    } catch (error) {
      console.error("计算价格失败:", error);
      this.setData({ calculating: false });
    }
  },

  // 收藏/取消收藏
  async onFavoriteTap() {
    try {
      const { isFavorited } = this.data;

      if (isFavorited) {
        await post(`/api/favorites/remove/${this.data.productId}`);
        wx.showToast({
          title: "已取消收藏",
          icon: "success",
        });
      } else {
        await post(`/api/favorites/add/${this.data.productId}`);
        wx.showToast({
          title: "已添加收藏",
          icon: "success",
        });
      }

      this.setData({ isFavorited: !isFavorited });
    } catch (error) {
      console.error("收藏操作失败:", error);
      wx.showToast({
        title: "操作失败",
        icon: "none",
      });
    }
  },

  // 立即下单（附带产品对象，订单页无需再请求）
  onOrderNow() {
    const product = this.data.product || {
      id: this.data.productId,
      name: "定制商品",
      description: "根据您的下单参数进行定制生产",
      price: "2.50",
      unit: "件",
      image:
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCI+PHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0iI0Y4RkFGQyIvPjwvc3ZnPi==",
      minQuantity: 1,
    };

    const orderData = {
      product,
      quantity: this.data.quantity,
      specifications: this.data.selectedSpecs,
    };

    const params = encodeURIComponent(JSON.stringify(orderData));
    wx.navigateTo({ url: `/pages/order/order?orderData=${params}` });
  },

  // 加入购物车
  async onAddToCart() {
    try {
      const cartData = {
        productId: this.data.productId,
        quantity: this.data.quantity,
        specifications: this.data.selectedSpecs,
      };

      await post("/api/cart/add", cartData);

      wx.showToast({
        title: "已加入购物车",
        icon: "success",
      });
    } catch (error) {
      console.error("加入购物车失败:", error);
      wx.showToast({
        title: "添加失败",
        icon: "none",
      });
    }
  },

  // 联系客服
  onContactService() {
    wx.navigateTo({
      url: `/pages/contact/contact?productId=${this.data.productId}`,
    });
  },

  // 相关产品点击
  onRelatedProductTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.redirectTo({
      url: `/pages/product-detail/product-detail?id=${id}`,
    });
  },

  // 查看更多评价
  onMoreReviews() {
    wx.navigateTo({
      url: `/pages/product-reviews/product-reviews?productId=${this.data.productId}`,
    });
  },

  // 分享
  onShareAppMessage() {
    const { product } = this.data;
    return {
      title: product ? `${product.name} - 小森包装` : "小森包装产品",
      path: `/pages/product-detail/product-detail?id=${this.data.productId}`,
      imageUrl: product?.images?.[0] || "/images/share-product.jpg",
    };
  },
});
