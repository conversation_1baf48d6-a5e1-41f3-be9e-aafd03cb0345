// 订单详情页面
const { get, post } = require("../../utils/request");
const { formatPrice, copyToClipboard } = require("../../utils/util");

Page({
  data: {
    // 订单ID
    orderId: "",

    // 订单详情
    order: null,

    // 加载状态
    loading: true,

    // 订单状态映射
    statusMap: {
      pending: { text: "待付款", color: "#F59E0B" },
      paid: { text: "已付款", color: "#10B981" },
      producing: { text: "生产中", color: "#3B82F6" },
      shipping: { text: "配送中", color: "#8B5CF6" },
      completed: { text: "已完成", color: "#10B981" },
      cancelled: { text: "已取消", color: "#EF4444" },
    },
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ orderId: options.id });
      this.loadOrderDetail();
    }
  },

  onPullDownRefresh() {
    this.loadOrderDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      this.setData({ loading: true });
      const order = await get(`/api/orders/${this.data.orderId}`);

      this.setData({
        order,
        loading: false,
      });
    } catch (error) {
      console.error("加载订单详情失败:", error);
      this.setData({ loading: false });
      wx.showToast({
        title: "加载失败",
        icon: "none",
      });
    }
  },

  // 复制订单号
  onCopyOrderNo() {
    copyToClipboard(this.data.order.orderNo);
  },

  // 取消订单
  async onCancelOrder() {
    const confirmed = await new Promise((resolve) => {
      wx.showModal({
        title: "确认取消",
        content: "确定要取消这个订单吗？",
        success: (res) => resolve(res.confirm),
      });
    });

    if (!confirmed) return;

    try {
      await post(`/api/orders/${this.data.orderId}/cancel`);

      wx.showToast({
        title: "订单已取消",
        icon: "success",
      });

      // 刷新订单详情
      this.loadOrderDetail();
    } catch (error) {
      console.error("取消订单失败:", error);
      wx.showToast({
        title: "取消失败",
        icon: "none",
      });
    }
  },

  // 立即支付
  async onPayNow() {
    try {
      const paymentData = await post(`/api/orders/${this.data.orderId}/pay`);

      wx.requestPayment({
        ...paymentData,
        success: () => {
          wx.showToast({
            title: "支付成功",
            icon: "success",
          });
          this.loadOrderDetail();
        },
        fail: (err) => {
          if (err.errMsg !== "requestPayment:fail cancel") {
            wx.showToast({
              title: "支付失败",
              icon: "none",
            });
          }
        },
      });
    } catch (error) {
      console.error("发起支付失败:", error);
      wx.showToast({
        title: "支付失败",
        icon: "none",
      });
    }
  },

  // 确认收货
  async onConfirmReceive() {
    const confirmed = await new Promise((resolve) => {
      wx.showModal({
        title: "确认收货",
        content: "确认已收到货物吗？",
        success: (res) => resolve(res.confirm),
      });
    });

    if (!confirmed) return;

    try {
      await post(`/api/orders/${this.data.orderId}/confirm-receive`);

      wx.showToast({
        title: "确认收货成功",
        icon: "success",
      });

      this.loadOrderDetail();
    } catch (error) {
      console.error("确认收货失败:", error);
      wx.showToast({
        title: "操作失败",
        icon: "none",
      });
    }
  },

  // 查看物流
  onViewLogistics() {
    if (!this.data.order.trackingNo) {
      wx.showToast({
        title: "暂无物流信息",
        icon: "none",
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/logistics/logistics?trackingNo=${this.data.order.trackingNo}`,
    });
  },

  // 联系客服
  onContactService() {
    wx.navigateTo({
      url: `/pages/contact/contact?orderId=${this.data.orderId}`,
    });
  },

  // 再次购买
  onBuyAgain() {
    const { order } = this.data;
    const orderData = {
      productId: order.productId,
      quantity: order.quantity,
      specifications: order.specifications,
    };

    const params = encodeURIComponent(JSON.stringify(orderData));
    wx.navigateTo({
      url: `/pages/order/order?orderData=${params}`,
    });
  },
});
