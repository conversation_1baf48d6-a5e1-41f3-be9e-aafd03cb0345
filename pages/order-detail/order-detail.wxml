<!-- 订单详情 -->
<view class="page-container">
  <!-- 头部状态卡片 -->
  <view class="header">
    <view class="status">
      <text class="title">订单号：{{order.orderNo || orderId}}</text>
      <text class="status-badge" style="background: {{statusMap[order.status].color}}">{{statusMap[order.status].text}}</text>
    </view>
    <view class="meta">下单时间：{{order.createdAt || '--'}}</view>
    <view class="meta">当前进度：</view>
    <view class="progress">
      <view class="step {{order.status !== 'pending' ? 'active' : ''}}"></view>
      <view class="step {{order.status === 'paid' || order.status === 'producing' || order.status === 'shipping' || order.status === 'completed' ? 'active' : ''}}"></view>
      <view class="step {{order.status === 'producing' || order.status === 'shipping' || order.status === 'completed' ? 'active' : ''}}"></view>
      <view class="step {{order.status === 'shipping' || order.status === 'completed' ? 'active' : ''}}"></view>
      <view class="step {{order.status === 'completed' ? 'active' : ''}}"></view>
    </view>
  </view>

  <!-- 商品信息 -->
  <view class="card">
    <view class="card-body">
      <text class="title">商品信息</text>
      <view class="product">
        <image src="{{order.productImage}}" mode="aspectFill" />
        <view class="flex-1">
          <view class="row">
            <text>{{order.productName || '定制商品'}}</text>
            <text class="gray">x{{order.quantity}}</text>
          </view>
          <view class="row gray">规格：{{order.specifications || '--'}}</view>
          <view class="row gray">收货人：{{order.receiverName}} {{order.receiverPhone}}</view>
          <view class="row gray">地址：{{order.address}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 价格明细 -->
  <view class="card">
    <view class="card-body">
      <text class="title">价格明细</text>
      <view class="row"><text class="gray">商品小计</text><text>¥{{order.subtotal}}</text></view>
      <view class="row" wx:if="{{order.urgentFee && order.urgentFee > 0}}"><text class="gray">加急费用</text><text>¥{{order.urgentFee}}</text></view>
      <view class="row"><text class="gray">运费</text><text>¥{{order.deliveryFee}}</text></view>
      <view class="row" wx:if="{{order.discountAmount && order.discountAmount > 0}}"><text class="gray">优惠金额</text><text>-¥{{order.discountAmount}}</text></view>
      <view class="row"><text class="title">实付金额</text><text class="title">¥{{order.totalAmount}}</text></view>
    </view>
  </view>

  <!-- 操作区 -->
  <view class="footer-bar">
    <button class="btn outline" bindtap="onContactService">联系客服</button>
    <button class="btn outline" bindtap="onViewLogistics">查看物流</button>
    <button class="btn primary" wx:if="{{order.status === 'pending'}}" bindtap="onPayNow">立即支付</button>
    <button class="btn primary" wx:elif="{{order.status === 'shipping'}}" bindtap="onConfirmReceive">确认收货</button>
    <button class="btn primary" wx:elif="{{order.status === 'completed'}}" bindtap="onBuyAgain">再次购买</button>
  </view>
</view>