/* 订单详情 */
.page-container { background:#f8fafc; min-height:100vh; padding-bottom: 160rpx; }
.header { background:#fff; padding:32rpx; margin: 24rpx 32rpx; border-radius:16rpx; box-shadow: 0 4rpx 12rpx rgba(0,0,0,.05); }
.status { display:flex; justify-content:space-between; align-items:center; }
.status-badge { padding: 8rpx 16rpx; border-radius: 20rpx; color:#fff; font-size: 22rpx; }
.meta { margin-top: 16rpx; color: var(--text-secondary); font-size: 24rpx; }
.card { background:#fff; margin: 24rpx 32rpx; border-radius:16rpx; box-shadow: 0 4rpx 12rpx rgba(0,0,0,.05); }
.card .card-body { padding: 24rpx 32rpx; }
.title { font-size: 30rpx; font-weight: 600; margin-bottom: 16rpx; }
.row { display:flex; justify-content: space-between; align-items:center; padding: 12rpx 0; }
.gray { color: var(--text-secondary); }
.product { display:flex; gap: 16rpx; }
.product img, .product image { width: 120rpx; height: 120rpx; border-radius: 12rpx; }
.footer-bar { position:fixed; left:0; right:0; bottom:0; background:#fff; padding: 20rpx 24rpx; border-top:1px solid var(--border-color); display:flex; gap: 16rpx; }
.btn { flex:1; height: 88rpx; border-radius: 12rpx; font-size: 28rpx; font-weight: 600; }
.btn.primary { background: var(--primary-color); color:#fff; }
.btn.outline { background: #fff; color: var(--primary-color); border:2rpx solid var(--primary-color); }
.progress { display:flex; gap: 12rpx; margin-top: 8rpx; }
.step { flex:1; height: 8rpx; background: var(--gray-200); border-radius: 8rpx; }
.step.active { background: var(--primary-color); }

