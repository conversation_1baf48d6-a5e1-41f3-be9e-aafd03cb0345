// 联系客服页面
Page({
  data: {
    // 联系方式
    contactMethods: [
      {
        id: "phone",
        title: "电话咨询",
        subtitle: "工作时间：9:00-18:00",
        value: "18854121057",
        icon: "/images/icons/phone.png",
      },
      {
        id: "wechat",
        title: "微信客服",
        subtitle: "添加微信好友",
        value: "xiaosen2024",
        icon: "/images/icons/wechat.png",
      },
      {
        id: "qq",
        title: "QQ客服",
        subtitle: "在线咨询",
        value: "123456789",
        icon: "/images/icons/qq.png",
      },
      {
        id: "email",
        title: "邮箱联系",
        subtitle: "24小时内回复",
        value: "<EMAIL>",
        icon: "/images/icons/email.png",
      },
    ],

    // 常见问题
    faqList: [
      {
        question: "如何下单？",
        answer: "您可以通过产品页面直接下单，或使用价格计算器获取报价后下单。",
      },
      {
        question: "交期多长？",
        answer:
          "正常交期为7-10个工作日，加急订单3-5个工作日，特急订单1-2个工作日。",
      },
      {
        question: "如何付款？",
        answer: "支持微信支付、支付宝、银行转账等多种付款方式。",
      },
      {
        question: "可以提供样品吗？",
        answer: "可以提供免费样品，样品制作时间1-3个工作日。",
      },
    ],
  },

  onLoad(options) {
    // 如果有产品ID，可以预填相关信息
    if (options.productId) {
      console.log("咨询产品ID:", options.productId);
    }
  },

  // 联系方式点击
  onContactTap(e) {
    const { method } = e.currentTarget.dataset;

    switch (method.id) {
      case "phone":
        wx.makePhoneCall({
          phoneNumber: method.value,
        });
        break;
      case "wechat":
        wx.setClipboardData({
          data: method.value,
          success: () => {
            wx.showModal({
              title: "微信号已复制",
              content: "请打开微信添加好友",
              showCancel: false,
            });
          },
        });
        break;
      case "qq":
        wx.setClipboardData({
          data: method.value,
          success: () => {
            wx.showToast({
              title: "QQ号已复制",
              icon: "success",
            });
          },
        });
        break;
      case "email":
        wx.setClipboardData({
          data: method.value,
          success: () => {
            wx.showToast({
              title: "邮箱已复制",
              icon: "success",
            });
          },
        });
        break;
    }
  },

  // 在线客服
  onOnlineService() {
    wx.showToast({
      title: "客服功能开发中",
      icon: "none",
    });
  },
});
