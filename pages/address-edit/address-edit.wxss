/* pages/address-edit/address-edit.wxss */
.page-container {
  padding: 32rpx;
}

.form-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 0 32rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}

.input, .picker-value {
  flex: 1;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
}

.textarea-item {
  align-items: flex-start;
}

.textarea {
  height: 160rpx;
  padding-top: 0;
}

.switch-item .label {
  flex: 1;
}

.toolbar {
  margin-top: 64rpx;
}
