// pages/address-edit/address-edit.js
const { get, post, put } = require("../../utils/request");

Page({
  data: {
    id: null,
    name: "",
    phone: "",
    province: "",
    city: "",
    district: "",
    detail: "",
    isDefault: false,
    tag: "",
    region: [],
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ id: options.id });
      this.loadAddressDetail();
      wx.setNavigationBarTitle({ title: "编辑地址" });
    } else {
      wx.setNavigationBarTitle({ title: "新增地址" });
    }
  },

  async loadAddressDetail() {
    try {
      const address = await get(`/api/user/addresses/${this.data.id}`);
      this.setData({
        ...address,
        region: [address.province, address.city, address.district],
      });
    } catch (error) {
      console.error("加载地址详情失败:", error);
    }
  },

  onRegionChange(e) {
    const [province, city, district] = e.detail.value;
    this.setData({ region: e.detail.value, province, city, district });
  },

  async onSave() {
    const { id, name, phone, province, city, district, detail, isDefault, tag } = this.data;

    if (!name || !phone || !detail || !province) {
      wx.showToast({ title: "请填写完整信息", icon: "none" });
      return;
    }

    const addressData = { name, phone, province, city, district, detail, isDefault, tag };

    try {
      if (id) {
        await put(`/api/user/addresses/${id}`, addressData);
      } else {
        await post("/api/user/addresses", addressData);
      }
      wx.showToast({ title: "保存成功", icon: "success" });
      wx.navigateBack();
    } catch (error) {
      console.error("保存地址失败:", error);
      wx.showToast({ title: "保存失败", icon: "none" });
    }
  },
});
