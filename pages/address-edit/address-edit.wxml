<!-- pages/address-edit/address-edit.wxml -->
<view class="page-container">
  <view class="form-card">
    <view class="form-item">
      <text class="label">收货人</text>
      <input class="input" placeholder="请输入姓名" value="{{name}}" bindinput="onInput" data-field="name" />
    </view>
    <view class="form-item">
      <text class="label">手机号</text>
      <input class="input" type="number" placeholder="请输入手机号" value="{{phone}}" bindinput="onInput" data-field="phone" />
    </view>
    <view class="form-item">
      <text class="label">所在地区</text>
      <picker mode="region" bindchange="onRegionChange" value="{{region}}">
        <view class="picker-value {{region.length ? '' : 'placeholder'}}">
          {{region.length ? region.join(' ') : '请选择省市区'}}
        </view>
      </picker>
    </view>
    <view class="form-item textarea-item">
      <text class="label">详细地址</text>
      <textarea class="textarea" placeholder="请输入详细地址" value="{{detail}}" bindinput="onInput" data-field="detail"></textarea>
    </view>
    <view class="form-item">
      <text class="label">标签</text>
      <input class="input" placeholder="例如：家、公司" value="{{tag}}" bindinput="onInput" data-field="tag" />
    </view>
    <view class="form-item switch-item">
      <text class="label">设为默认地址</text>
      <switch checked="{{isDefault}}" bindchange="onSwitchChange" data-field="isDefault" />
    </view>
  </view>

  <view class="toolbar">
    <button class="primary" bindtap="onSave">保存</button>
  </view>
</view>
