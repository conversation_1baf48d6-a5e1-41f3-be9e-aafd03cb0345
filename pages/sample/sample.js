// 样品申请页面
const { post } = require("../../utils/request");

Page({
  data: {
    // 申请信息
    sampleInfo: {
      productType: "",
      quantity: 1,
      specifications: "",
      purpose: "",
      contactName: "",
      contactPhone: "",
      address: "",
      remarks: "",
    },

    // 产品类型选项
    productTypes: ["包装盒", "手提袋", "宣传册", "名片", "标签贴纸", "其他"],

    // 申请目的选项
    purposeOptions: [
      "质量确认",
      "设计参考",
      "客户展示",
      "批量采购前确认",
      "其他",
    ],

    // 提交状态
    submitting: false,
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: "免费样品申请",
    });
  },

  // 产品类型选择
  onProductTypeChange(e) {
    const index = e.detail.value;
    this.setData({
      "sampleInfo.productType": this.data.productTypes[index],
    });
  },

  // 申请目的选择
  onPurposeChange(e) {
    const index = e.detail.value;
    this.setData({
      "sampleInfo.purpose": this.data.purposeOptions[index],
    });
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`sampleInfo.${field}`]: value,
    });
  },

  // 数量变化
  onQuantityChange(e) {
    const { type } = e.currentTarget.dataset;
    let quantity = this.data.sampleInfo.quantity;

    if (type === "minus" && quantity > 1) {
      quantity--;
    } else if (type === "plus" && quantity < 10) {
      quantity++;
    }

    this.setData({
      "sampleInfo.quantity": quantity,
    });
  },

  // 提交申请
  async onSubmit() {
    if (!this.validateForm()) {
      return;
    }

    try {
      this.setData({ submitting: true });

      await post("/api/samples/apply", this.data.sampleInfo);

      wx.showToast({
        title: "申请成功",
        icon: "success",
      });

      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error("提交申请失败:", error);
      wx.showToast({
        title: "提交失败",
        icon: "none",
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 表单验证
  validateForm() {
    const { sampleInfo } = this.data;

    if (!sampleInfo.productType) {
      wx.showToast({
        title: "请选择产品类型",
        icon: "none",
      });
      return false;
    }

    if (!sampleInfo.contactName) {
      wx.showToast({
        title: "请输入联系人姓名",
        icon: "none",
      });
      return false;
    }

    if (!sampleInfo.contactPhone) {
      wx.showToast({
        title: "请输入联系电话",
        icon: "none",
      });
      return false;
    }

    if (!/^1[3-9]\d{9}$/.test(sampleInfo.contactPhone)) {
      wx.showToast({
        title: "手机号格式不正确",
        icon: "none",
      });
      return false;
    }

    if (!sampleInfo.address) {
      wx.showToast({
        title: "请输入收货地址",
        icon: "none",
      });
      return false;
    }

    return true;
  },
});
