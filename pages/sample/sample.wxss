/* 样品申请页 */
.page-container { background: #f8fafc; min-height: 100vh; padding-bottom: 120rpx; }
.card { background: #fff; margin: 24rpx 32rpx; border-radius: 16rpx; box-shadow: 0 4rpx 12rpx rgba(0,0,0,.05); padding: 24rpx; }
.section-title { font-size: 32rpx; font-weight: 600; color: var(--text-primary); margin-bottom: 24rpx; text-align: center; }
.form-item { margin-bottom: 20rpx; }
.label { display: block; font-size: 26rpx; color: var(--text-secondary); margin-bottom: 10rpx; }
.input, .textarea, .picker { width: 100%; border: 2rpx solid var(--border-color); border-radius: 8rpx; }
.input { height: 80rpx; padding: 0 20rpx; font-size: 28rpx; }
.textarea { min-height: 160rpx; padding: 16rpx 20rpx; font-size: 26rpx; }
.picker { padding: 20rpx; font-size: 26rpx; background: var(--gray-50); }
.qty-row { display: flex; justify-content: space-between; align-items: center; }
.qty-ctrl { display: flex; align-items: center; border: 2rpx solid var(--border-color); border-radius: 8rpx; overflow: hidden; }
.qty-btn { width: 60rpx; height: 60rpx; display:flex; align-items:center; justify-content:center; background: var(--gray-100); }
.qty-input { width: 120rpx; height: 60rpx; text-align: center; border: none; font-size: 28rpx; }
.submit { position: fixed; left: 0; right: 0; bottom: 0; padding: 24rpx 32rpx; background: #fff; border-top: 1px solid var(--border-color); }
.primary { width: 100%; height: 88rpx; border-radius: 12rpx; background: var(--primary-color); color:#fff; font-size: 32rpx; font-weight: 700; }

