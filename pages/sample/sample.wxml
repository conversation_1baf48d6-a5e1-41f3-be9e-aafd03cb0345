<!-- 免费样品申请 -->
<view class="page-container">
  <view class="card">
    <view class="section-title">免费样品申请</view>

    <view class="form-item">
      <text class="label">产品类型</text>
      <picker range="{{productTypes}}" bindchange="onProductTypeChange">
        <view class="picker">{{sampleInfo.productType || '请选择产品类型'}}</view>
      </picker>
    </view>

    <view class="form-item qty-row">
      <text class="label">数量</text>
      <view class="qty-ctrl">
        <view class="qty-btn" bindtap="onQuantityChange" data-type="minus">-</view>
        <input class="qty-input" type="number" value="{{sampleInfo.quantity}}" disabled />
        <view class="qty-btn" bindtap="onQuantityChange" data-type="plus">+</view>
      </view>
    </view>

    <view class="form-item">
      <text class="label">尺寸/规格</text>
      <input class="input" placeholder="如：20×15×8cm / 300gsm卡纸" data-field="specifications" bindinput="onInputChange" />
    </view>

    <view class="form-item">
      <text class="label">用途</text>
      <picker range="{{purposeOptions}}" bindchange="onPurposeChange">
        <view class="picker">{{sampleInfo.purpose || '请选择用途'}}</view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">联系人</text>
      <input class="input" placeholder="请输入姓名" data-field="contactName" bindinput="onInputChange" />
    </view>

    <view class="form-item">
      <text class="label">联系电话</text>
      <input class="input" type="number" placeholder="请输入手机号" data-field="contactPhone" bindinput="onInputChange" />
    </view>

    <view class="form-item">
      <text class="label">收货地址</text>
      <input class="input" placeholder="用于邮寄样品，请详细填写" data-field="address" bindinput="onInputChange" />
    </view>

    <view class="form-item">
      <text class="label">备注</text>
      <textarea class="textarea" placeholder="可填写其他要求" data-field="remarks" bindinput="onInputChange"></textarea>
    </view>
  </view>

  <view class="submit">
    <button class="primary" bindtap="onSubmit" loading="{{submitting}}">提交申请</button>
  </view>
</view>