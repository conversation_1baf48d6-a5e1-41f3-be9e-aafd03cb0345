/* 我的订单列表 */
.page-container { background:#f8fafc; min-height:100vh; }
.tabs { display:flex; background:#fff; border-bottom:1px solid var(--border-color); }
.tab { flex:1; text-align:center; padding:24rpx 0; font-size:28rpx; color:var(--text-secondary); }
.tab.active { color:var(--primary-color); font-weight:600; border-bottom:4rpx solid var(--primary-color); }
.list { padding: 24rpx 32rpx; }
.order-card { background:#fff; border-radius:16rpx; box-shadow:0 4rpx 12rpx rgba(0,0,0,.05); margin-bottom:24rpx; overflow:hidden; }
.order-header { display:flex; justify-content:space-between; align-items:center; padding:24rpx; border-bottom:1px solid var(--border-color); }
.order-no { font-size:26rpx; color:var(--text-secondary); }
.status { font-size:24rpx; }
.order-body { display:flex; align-items:center; gap:16rpx; padding:24rpx; }
.thumb { width:120rpx; height:120rpx; border-radius:12rpx; }
.meta { flex:1; }
.name { display:block; font-size:30rpx; color:var(--text-primary); font-weight:600; margin-bottom:8rpx; }
.spec, .qty { display:block; font-size:24rpx; color:var(--text-secondary); }
.total { display:flex; flex-direction:column; align-items:flex-end; }
.label { font-size:22rpx; color:var(--text-secondary); }
.value { font-size:32rpx; color:var(--primary-color); font-weight:700; }
.loading, .no-more { text-align:center; color:var(--text-secondary); padding:24rpx; }
.empty { display:flex; flex-direction:column; align-items:center; justify-content:center; padding:160rpx 0; }
.empty-icon { width:200rpx; height:200rpx; opacity:.6; margin-bottom:16rpx; }
.empty-text { font-size:28rpx; color:var(--text-secondary); }

