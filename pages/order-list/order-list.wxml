<!-- 我的订单列表 -->
<view class="page-container">
  <view class="tabs">
    <view class="tab {{currentTab === item.id ? 'active' : ''}}"
          wx:for="{{tabs}}" wx:key="id"
          bindtap="onTabChange" data-id="{{item.id}}">{{item.name}}</view>
  </view>

  <view class="list">
    <block wx:if="{{orders.length}}">
      <view class="order-card" wx:for="{{orders}}" wx:key="id" bindtap="onOrderTap" data-id="{{item.id}}">
        <view class="order-header">
          <text class="order-no">订单号：{{item.orderNo}}</text>
          <text class="status" style="color: {{statusMap[item.status].color}}">{{statusMap[item.status].text}}</text>
        </view>
        <view class="order-body">
          <image class="thumb" src="{{item.productImage}}" mode="aspectFill" />
          <view class="meta">
            <text class="name">{{item.productName}}</text>
            <text class="spec">规格：{{item.specifications || '--'}}</text>
            <text class="qty">数量：{{item.quantity}}</text>
          </view>
          <view class="total">
            <text class="label">实付</text>
            <text class="value">¥{{item.totalAmount}}</text>
          </view>
        </view>
      </view>

      <view class="loading" wx:if="{{loading}}">加载中...</view>
      <view class="no-more" wx:if="{{!hasMore && !loading}}">没有更多订单了</view>
    </block>

    <block wx:else>
      <view class="empty">
        <image class="empty-icon" src="/images/empty/orders.png" />
        <text class="empty-text">暂无订单</text>
      </view>
    </block>
  </view>
</view>
