// 我的订单列表
const { get } = require('../../utils/request')

Page({
  data: {
    tabs: [
      { id: 'all', name: '全部' },
      { id: 'pending', name: '待付款' },
      { id: 'paid', name: '待生产' },
      { id: 'producing', name: '生产中' },
      { id: 'shipping', name: '配送中' },
      { id: 'completed', name: '已完成' },
    ],
    currentTab: 'all',

    statusMap: {
      pending: { text: '待付款', color: '#F59E0B' },
      paid: { text: '待生产', color: '#10B981' },
      producing: { text: '生产中', color: '#3B82F6' },
      shipping: { text: '配送中', color: '#8B5CF6' },
      completed: { text: '已完成', color: '#10B981' },
      cancelled: { text: '已取消', color: '#EF4444' },
    },

    orders: [],
    page: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
  },

  onLoad(options) {
    if (options && options.status) {
      this.setData({ currentTab: options.status })
    }
    // 默认先用本地mock渲染一次
    this.setData({ orders: this.getMockOrders().slice(0, this.data.pageSize) })
    this.loadOrders(true)
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrders(false)
    }
  },

  onTabChange(e) {
    const id = e.currentTarget.dataset.id
    if (id === this.data.currentTab) return
    this.setData({ currentTab: id, page: 1 })
    this.loadOrders(true)
  },

  // 本地mock订单
  getMockOrders() {
    const base = [
      { id: 'A1001', orderNo: 'XS20250001', status: 'pending', productName: '定制商品', quantity: 2, totalAmount: '35.60', productImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjAwJyBoZWlnaHQ9JzE1MCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nI0Y4RkFGQycvPjwvc3ZnPg==', specifications: '默认' },
      { id: 'A1002', orderNo: 'XS20250002', status: 'paid', productName: '海鲜礼盒-三文鱼冻品', quantity: 1, totalAmount: '56.00', productImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjAwJyBoZWlnaHQ9JzE1MCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nIzAwQkNENCcvPjwvc3ZnPg==', specifications: '常规' },
      { id: 'A1003', orderNo: 'XS20250003', status: 'producing', productName: '月饼礼盒-双层抽屉式', quantity: 3, totalAmount: '66.00', productImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjAwJyBoZWlnaHQ9JzE1MCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nIzYzNjZGNycvPjwvc3ZnPg==', specifications: '烫金' },
      { id: 'A1004', orderNo: 'XS20250004', status: 'shipping', productName: '物流纸箱-五层加厚', quantity: 5, totalAmount: '18.00', productImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjAwJyBoZWlnaHQ9JzE1MCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nIzY2Nzg5MicvPjwvc3ZnPg==', specifications: 'K=K' },
      { id: 'A1005', orderNo: 'XS20250005', status: 'completed', productName: '手提袋-白卡纸', quantity: 10, totalAmount: '18.00', productImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjAwJyBoZWlnaHQ9JzE1MCc+PHJlY3Qgd2lkdGg9JzEwMCUnIGhlaWdodD0nMTAwJScgZmlsbD0nI0UxRTNFQScvPjwvc3ZnPg==', specifications: '扁绳' },
    ]
    // 根据tab过滤
    const list = this.data.currentTab === 'all' ? base : base.filter(o => o.status === this.data.currentTab)
    return list
  },

  async loadOrders(reset = false) {
    if (this.data.loading) return
    this.setData({ loading: true })

    try {
      const page = reset ? 1 : this.data.page + 1
      const params = { page, pageSize: this.data.pageSize, status: this.data.currentTab === 'all' ? undefined : this.data.currentTab }
      const result = await get('/api/orders', params)
      const incoming = result.list || []

      this.setData({
        orders: reset ? incoming : [...this.data.orders, ...incoming],
        page,
        hasMore: incoming.length === this.data.pageSize,
        loading: false,
      })
    } catch (e) {
      // 兜底用本地mock分页
      const all = this.getMockOrders()
      const start = reset ? 0 : this.data.page * this.data.pageSize
      const slice = all.slice(start, start + this.data.pageSize)
      this.setData({
        orders: reset ? slice : [...this.data.orders, ...slice],
        page: reset ? 1 : this.data.page + 1,
        hasMore: start + this.data.pageSize < all.length,
        loading: false,
      })
    }
  },

  onOrderTap(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({ url: `/pages/order-detail/order-detail?id=${id}` })
  }
})

