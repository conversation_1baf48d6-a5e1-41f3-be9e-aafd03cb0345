/* 个人中心页面样式 */

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, var(--primary-color), #3B82F6);
  padding: 60rpx 32rpx 40rpx;
  color: var(--white);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 32rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.user-phone {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 登录提示 */
.login-prompt {
  display: flex;
  align-items: center;
}

.default-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  opacity: 0.6;
  margin-right: 32rpx;
}

.login-info {
  flex: 1;
}

.login-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.login-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.login-btn {
  padding: 16rpx 32rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--white);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 48rpx;
  font-size: 26rpx;
}

.login-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 订单状态 */
.order-status {
  margin: 24rpx 32rpx;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.status-more {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  position: relative;
}

.status-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
}

.status-name {
  font-size: 24rpx;
  color: var(--text-primary);
}

.status-count {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background-color: var(--error-color);
  color: var(--white);
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

/* 统计数据 */
.statistics {
  display: flex;
  margin: 24rpx 32rpx;
  padding: 32rpx 0;
}

.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 功能菜单 */
.menu-section {
  margin: 24rpx 32rpx;
}

.menu-group {
  margin-bottom: 24rpx;
}

.menu-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-primary);
  padding: 24rpx 32rpx 16rpx;
  border-bottom: 1px solid var(--border-color);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.3s ease;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: var(--gray-50);
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-primary);
}

.menu-badge {
  background-color: var(--error-color);
  color: var(--white);
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  min-width: 32rpx;
  text-align: center;
}

.menu-arrow {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 退出登录 */
.logout-section {
  margin: 48rpx 32rpx 32rpx;
}

.logout-btn {
  width: 100%;
  height: 88rpx;
  background-color: var(--white);
  color: var(--error-color);
  border: 2rpx solid var(--error-color);
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.logout-btn:active {
  background-color: var(--error-color);
  color: var(--white);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .statistics {
    flex-wrap: wrap;
  }
  
  .stat-item {
    width: 50%;
    margin-bottom: 24rpx;
  }
}
