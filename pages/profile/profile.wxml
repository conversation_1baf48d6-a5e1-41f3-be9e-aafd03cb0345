<!-- 个人中心页面 -->
<view class="page-container">
  <!-- 顶部用户区（渐变背景） -->
  <view class="user-section">
    <view class="user-info" wx:if="{{isLogin}}">
      <image class="user-avatar" src="{{userInfo.avatarUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSI2MCIgZmlsbD0iI0Y5RkFGQiIvPjxjaXJjbGUgY3g9IjYwIiBjeT0iNDUiIHI9IjIwIiBmaWxsPSIjRDFEOUU2Ii8+PHBhdGggZD0iTTMwIDkwYzAtMTUgMTMuNS0yNSAzMC0yNXMyNSAxMCAzMCAyNSIgZmlsbD0iI0QxRDlFNiIvPjwvc3ZnPg=='}}" />
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName || '用户'}}</text>
        <text class="user-phone">{{userInfo.phone || '未绑定手机号'}}</text>
      </view>
    </view>

    <view class="login-prompt" wx:else>
      <image class="default-avatar" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI2MCIgY3k9IjYwIiByPSI2MCIgZmlsbD0iI0Y5RkFGQiIvPjxjaXJjbGUgY3g9IjYwIiBjeT0iNDUiIHI9IjIwIiBmaWxsPSIjRDFEOUU2Ii8+PHBhdGggZD0iTTMwIDkwYzAtMTUgMTMuNS0yNSAzMC0yNXMyNSAxMCAzMCAyNSIgZmlsbD0iI0QxRDlFNiIvPjwvc3ZnPg==" />
      <view class="login-info">
        <text class="login-title">欢迎使用济南小森</text>
        <text class="login-subtitle">登录后享受更多服务</text>
      </view>
      <button class="login-btn" bindtap="onLogin">立即登录</button>
    </view>
  </view>

  <!-- 订单状态 -->
  <view class="order-status card" wx:if="{{isLogin}}">
    <view class="status-header">
      <text class="status-title">我的订单</text>
      <text class="status-more" bindtap="onMenuTap" data-item="{{menuList[0]}}">查看全部</text>
    </view>
    <view class="status-grid">
      <view
        class="status-item"
        wx:for="{{orderStatusList}}"
        wx:key="id"
        bindtap="onOrderStatusTap"
        data-status="{{item.id}}"
      >
        <image class="status-icon" src="{{item.icon}}" />
        <text class="status-name">{{item.title}}</text>
        <text class="status-count" wx:if="{{item.count > 0}}">{{item.count}}</text>
      </view>
    </view>
  </view>

  <!-- 统计数据 -->
  <view class="statistics card" wx:if="{{isLogin}}">
    <view class="stat-item">
      <text class="stat-number">{{statistics.orderCount}}</text>
      <text class="stat-label">总订单</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.favoriteCount}}</text>
      <text class="stat-label">收藏</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.couponCount}}</text>
      <text class="stat-label">优惠券</text>
    </view>
    <view class="stat-item">
      <text class="stat-number">{{statistics.pointsCount}}</text>
      <text class="stat-label">积分</text>
    </view>
  </view>

  <!-- 功能菜单分组 -->
  <view class="menu-section">
    <view class="menu-group card">
      <view class="menu-title">我的服务</view>
      <view
        class="menu-item"
        wx:for="{{menuList}}"
        wx:for-index="index"
        wx:key="id"
        wx:if="{{index < 6}}"
        bindtap="onMenuTap"
        data-item="{{item}}"
      >
        <image class="menu-icon" src="{{item.icon}}" />
        <text class="menu-text">{{item.title}}</text>
        <text class="menu-badge" wx:if="{{item.badge > 0}}">{{item.badge}}</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <view class="menu-group card">
      <view class="menu-title">帮助与设置</view>
      <view
        class="menu-item"
        wx:for="{{menuList}}"
        wx:for-index="index"
        wx:key="id"
        wx:if="{{index >= 6}}"
        bindtap="onMenuTap"
        data-item="{{item}}"
      >
        <image class="menu-icon" src="{{item.icon}}" />
        <text class="menu-text">{{item.title}}</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{isLogin}}">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>
</view>
