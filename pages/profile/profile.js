// 个人中心页面
const app = getApp();
const { get } = require("../../utils/request");

Page({
  data: {
    // 用户信息
    userInfo: null,
    isLogin: false,

    // 统计数据
    statistics: {
      orderCount: 0,
      favoriteCount: 0,
      couponCount: 0,
      pointsCount: 0,
    },

    // 菜单列表
    menuList: [
      {
        id: "orders",
        title: "我的订单",
        icon: "/images/icons/orders.png",
        url: "/pages/order-list/order-list",
        badge: 0,
      },
      {
        id: "favorites",
        title: "我的收藏",
        icon: "/images/icons/favorites.png",
        url: "/pages/favorites/favorites",
      },
      {
        id: "address",
        title: "收货地址",
        icon: "/images/icons/address.png",
        url: "/pages/address/address",
      },
      {
        id: "coupons",
        title: "优惠券",
        icon: "/images/icons/coupons.png",
        url: "/pages/coupons/coupons",
        badge: 0,
      },
      {
        id: "points",
        title: "积分商城",
        icon: "/images/icons/points.png",
        url: "/pages/points/points",
      },
      {
        id: "samples",
        title: "样品申请",
        icon: "/images/icons/samples.png",
        url: "/pages/sample/sample",
      },
      {
        id: "quotes",
        title: "我的报价",
        icon: "/images/icons/quotes.png",
        url: "/pages/quotes/quotes",
      },
      {
        id: "contact",
        title: "联系客服",
        icon: "/images/icons/contact.png",
        url: "/pages/contact/contact",
      },
      {
        id: "about",
        title: "关于我们",
        icon: "/images/icons/about.png",
        url: "/pages/about/about",
      },
      {
        id: "settings",
        title: "设置",
        icon: "/images/icons/settings.png",
        url: "/pages/settings/settings",
      },
    ],

    // 快捷订单状态
    orderStatusList: [
      {
        id: "pending",
        title: "待付款",
        icon: "/images/icons/pending.png",
        count: 0,
      },
      {
        id: "paid",
        title: "待生产",
        icon: "/images/icons/paid.png",
        count: 0,
      },
      {
        id: "producing",
        title: "生产中",
        icon: "/images/icons/producing.png",
        count: 0,
      },
      {
        id: "shipping",
        title: "待收货",
        icon: "/images/icons/shipping.png",
        count: 0,
      },
    ],
  },

  onLoad() {
    // 设置默认数据，避免页面空白
    console.log("Profile page onLoad");
    console.log("Initial data:", this.data);

    this.setData({
      isLogin: false,
      userInfo: {},
      statistics: {
        orderCount: 0,
        favoriteCount: 0,
        couponCount: 0,
        pointsCount: 0,
      },
    });

    console.log("Data after setData:", this.data);
    this.checkLoginStatus();
  },

  onShow() {
    this.checkLoginStatus();
    if (this.data.isLogin) {
      this.loadUserData();
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLogin = app.globalData.isLogin;
    const userInfo = app.globalData.userInfo;

    this.setData({
      isLogin,
      userInfo,
    });
  },

  // 加载用户数据
  async loadUserData() {
    try {
      // 并行加载用户统计数据
      const [statistics, orderCounts] = await Promise.all([
        this.loadStatistics(),
        this.loadOrderCounts(),
      ]);

      this.setData({
        statistics,
        orderStatusList: this.data.orderStatusList.map((item) => ({
          ...item,
          count: orderCounts[item.id] || 0,
        })),
        "menuList[0].badge": orderCounts.total || 0,
        "menuList[3].badge": statistics.couponCount || 0,
      });
    } catch (error) {
      console.error("加载用户数据失败:", error);
    }
  },

  // 加载统计数据
  async loadStatistics() {
    try {
      const data = await get("/api/user/statistics");
      return data || {};
    } catch (error) {
      console.error("加载统计数据失败:", error);
      return {};
    }
  },

  // 加载订单数量
  async loadOrderCounts() {
    try {
      const data = await get("/api/orders/counts");
      return data || {};
    } catch (error) {
      console.error("加载订单数量失败:", error);
      return {};
    }
  },

  // 登录
  onLogin() {
    // 从tabBar跳转到非tab页可能受限，失败则降级 reLaunch
    wx.navigateTo({
      url: "/pages/login/login",
      fail: () => wx.reLaunch({ url: "/pages/login/login" }),
    });
  },

  // 获取用户信息
  onGetUserProfile() {
    wx.getUserProfile({
      desc: "用于完善用户资料",
      success: (res) => {
        console.log("获取用户信息成功:", res.userInfo);
        // TODO: 将用户信息发送到服务器
        app.globalData.userInfo = res.userInfo;
        this.setData({
          userInfo: res.userInfo,
        });
      },
      fail: (err) => {
        console.error("获取用户信息失败:", err);
        wx.showToast({
          title: "获取用户信息失败",
          icon: "none",
        });
      },
    });
  },

  // 菜单项点击
  onMenuTap(e) {
    const { item } = e.currentTarget.dataset;

    // 需要登录的功能
    const needLoginMenus = [
      "orders",
      "favorites",
      "address",
      "coupons",
      "points",
      "quotes",
    ];

    if (needLoginMenus.includes(item.id) && !this.data.isLogin) {
      this.showLoginModal();
      return;
    }

    // 特殊处理
    if (item.id === "contact") {
      this.contactService();
      return;
    }

    wx.navigateTo({
      url: item.url,
    });
  },

  // 订单状态点击
  onOrderStatusTap(e) {
    const { status } = e.currentTarget.dataset;

    if (!this.data.isLogin) {
      this.showLoginModal();
      return;
    }

    wx.navigateTo({
      url: `/pages/order-list/order-list?status=${status}`,
    });
  },

  // 显示登录提示
  showLoginModal() {
    wx.showModal({
      title: "提示",
      content: "此功能需要登录后使用",
      confirmText: "去登录",
      success: (res) => {
        if (res.confirm) {
          this.onLogin();
        }
      },
    });
  },

  // 联系客服
  contactService() {
    wx.showActionSheet({
      itemList: ["在线客服", "电话咨询", "微信客服"],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.navigateTo({
              url: "/pages/contact/contact",
            });
            break;
          case 1:
            wx.makePhoneCall({
              phoneNumber: "18854121057",
            });
            break;
          case 2:
            wx.showModal({
              title: "微信客服",
              content: "请添加微信号：xiaosen2024",
              showCancel: false,
            });
            break;
        }
      },
    });
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: "确认退出",
      content: "确定要退出登录吗？",
      success: (res) => {
        if (res.confirm) {
          // 清除登录状态
          app.globalData.isLogin = false;
          app.globalData.userInfo = null;
          app.globalData.token = "";
          wx.removeStorageSync("token");

          this.setData({
            isLogin: false,
            userInfo: null,
            statistics: {
              orderCount: 0,
              favoriteCount: 0,
              couponCount: 0,
              pointsCount: 0,
            },
          });

          wx.showToast({
            title: "已退出登录",
            icon: "success",
          });
        }
      },
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: "济南小森 - 专业包装贸易服务",
      path: "/pages/index/index",
      imageUrl: "/images/share.jpg",
    };
  },
});
