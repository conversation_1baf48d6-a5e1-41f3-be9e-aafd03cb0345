// 网络请求工具类
const app = getApp();

/**
 * 封装微信请求
 */
function request(options) {
  return new Promise((resolve, reject) => {
    const {
      url,
      method = "GET",
      data = {},
      header = {},
      showLoading = false,
      loadingText = "加载中...",
    } = options;

    // 显示加载提示
    if (showLoading) {
      wx.showLoading({
        title: loadingText,
        mask: true,
      });
    }

    // 构建完整URL
    const fullUrl = url.startsWith("http")
      ? url
      : `${app.globalData.baseUrl}${url}`;

    // 设置请求头
    const requestHeader = {
      "Content-Type": "application/json",
      ...header,
    };

    // 添加token
    if (app.globalData.token) {
      requestHeader["Authorization"] = `Bearer ${app.globalData.token}`;
    }

    wx.request({
      url: fullUrl,
      method,
      data,
      header: requestHeader,
      success: (res) => {
        if (showLoading) {
          wx.hideLoading();
        }

        const { statusCode, data: responseData } = res;

        if (statusCode === 200) {
          // 请求成功
          if (responseData.code === 0) {
            resolve(responseData.data);
          } else {
            // 业务错误
            wx.showToast({
              title: responseData.message || "请求失败",
              icon: "none",
            });
            reject(responseData);
          }
        } else if (statusCode === 401) {
          // 未授权，清除登录状态
          app.globalData.isLogin = false;
          app.globalData.token = "";
          wx.removeStorageSync("token");
          wx.showToast({
            title: "请重新登录",
            icon: "none",
          });
          // 跳转到登录页
          wx.navigateTo({
            url: "/pages/login/login",
          });
          reject(res);
        } else {
          // 其他错误 - 静默处理，不显示错误提示
          console.error("网络错误:", res);
          reject(res);
        }
      },
      fail: (err) => {
        if (showLoading) {
          wx.hideLoading();
        }
        // 静默处理网络连接失败，不显示错误提示
        console.error("网络连接失败:", err);
        reject(err);
      },
    });
  });
}

/**
 * GET请求
 */
function get(url, data = {}, options = {}) {
  return request({
    url,
    method: "GET",
    data,
    ...options,
  });
}

/**
 * POST请求
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: "POST",
    data,
    ...options,
  });
}

/**
 * PUT请求
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: "PUT",
    data,
    ...options,
  });
}

/**
 * DELETE请求
 */
function del(url, data = {}, options = {}) {
  return request({
    url,
    method: "DELETE",
    data,
    ...options,
  });
}

/**
 * 上传文件
 */
function uploadFile(filePath, url, formData = {}) {
  return new Promise((resolve, reject) => {
    wx.showLoading({
      title: "上传中...",
      mask: true,
    });

    const fullUrl = url.startsWith("http")
      ? url
      : `${app.globalData.baseUrl}${url}`;

    wx.uploadFile({
      url: fullUrl,
      filePath,
      name: "file",
      formData,
      header: {
        Authorization: app.globalData.token
          ? `Bearer ${app.globalData.token}`
          : "",
      },
      success: (res) => {
        wx.hideLoading();
        try {
          const data = JSON.parse(res.data);
          if (data.code === 0) {
            resolve(data.data);
          } else {
            wx.showToast({
              title: data.message || "上传失败",
              icon: "none",
            });
            reject(data);
          }
        } catch (e) {
          wx.showToast({
            title: "上传失败",
            icon: "none",
          });
          reject(e);
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: "上传失败",
          icon: "none",
        });
        reject(err);
      },
    });
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  del,
  uploadFile,
};
