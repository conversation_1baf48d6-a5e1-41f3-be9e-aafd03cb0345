# 济南小森 - 微信小程序

## 项目简介

济南小森国际贸易有限公司是一个专业的包装贸易服务微信小程序，为客户提供包装盒、手提袋、宣传册、名片、标签贴纸等各类包装产品的贸易服务。

## 功能特色

### 🎯 核心功能

- **产品展示**：丰富的产品分类和详细展示
- **在线报价**：智能价格计算器，实时获取报价
- **快速下单**：简化的下单流程，支持自定义需求
- **订单跟踪**：实时查看生产进度和物流状态
- **样品申请**：免费样品申请和快递配送
- **在线客服**：专业客服团队，及时响应咨询

### 📱 页面结构

```
├── 首页 (index)
│   ├── 轮播图展示
│   ├── 快速入口
│   ├── 产品分类
│   ├── 热门产品
│   └── 企业动态
├── 产品中心 (products)
│   ├── 分类筛选
│   ├── 搜索功能
│   ├── 产品列表
│   └── 产品详情
├── 价格计算器 (calculator)
│   ├── 产品类型选择
│   ├── 规格参数设置
│   ├── 材质工艺选择
│   └── 实时价格计算
└── 个人中心 (profile)
    ├── 用户信息
    ├── 订单管理
    ├── 收藏夹
    └── 设置中心
```

### 🎨 设计特色

- **专业配色**：深蓝主色调体现专业可靠，橙色点缀增加活力
- **简洁界面**：清爽的视觉设计，优秀的用户体验
- **响应式布局**：适配不同屏幕尺寸的设备
- **流畅交互**：丰富的动画效果和交互反馈

## 技术架构

### 前端技术栈

- **框架**：微信小程序原生开发
- **样式**：WXSS + CSS3 变量
- **状态管理**：页面级数据管理
- **网络请求**：封装的 request 工具类
- **工具函数**：通用工具函数库

### 项目结构

```
xiaosen/
├── app.js                 # 小程序入口文件
├── app.json               # 小程序配置文件
├── app.wxss               # 全局样式文件
├── sitemap.json           # 站点地图配置
├── project.config.json    # 项目配置文件
├── utils/                 # 工具类目录
│   ├── request.js         # 网络请求封装
│   └── util.js            # 通用工具函数
├── pages/                 # 页面目录
│   ├── index/             # 首页
│   ├── products/          # 产品列表
│   ├── product-detail/    # 产品详情
│   ├── calculator/        # 价格计算器
│   ├── order/             # 下单页面
│   ├── profile/           # 个人中心
│   └── ...               # 其他页面
└── images/               # 图片资源目录
    ├── icons/            # 图标
    ├── banners/          # 轮播图
    ├── products/         # 产品图片
    └── ...              # 其他图片
```

## 开发指南

### 环境要求

- 微信开发者工具 1.05.0 或更高版本
- Node.js 14.0 或更高版本（如需要构建工具）

### 快速开始

1. 下载并安装微信开发者工具
2. 克隆或下载项目代码
3. 在微信开发者工具中导入项目
4. 配置小程序 AppID（在 project.config.json 中）
5. 配置后端 API 地址（在 app.js 中的 baseUrl）
6. 开始开发调试

### 配置说明

#### API 配置

在 `app.js` 中配置后端 API 基础地址：

```javascript
globalData: {
  baseUrl: 'https://api.xiaosen.com', // 修改为实际的API地址
  // ...
}
```

#### 小程序配置

在 `project.config.json` 中配置小程序 AppID：

```json
{
  "appid": "your-miniprogram-appid"
  // ...
}
```

### 开发规范

#### 代码规范

- 使用 ES6+ 语法
- 遵循微信小程序开发规范
- 保持代码简洁和可读性
- 添加必要的注释

#### 样式规范

- 使用 CSS 变量统一管理颜色和尺寸
- 采用 BEM 命名规范
- 保持样式的模块化和复用性

#### 文件命名

- 页面文件使用小写字母和连字符
- 组件文件使用 PascalCase
- 图片文件使用描述性命名

## API 接口

### 基础接口

- `GET /api/products` - 获取产品列表
- `GET /api/products/:id` - 获取产品详情
- `POST /api/calculator/calculate` - 价格计算
- `POST /api/orders` - 创建订单
- `GET /api/orders` - 获取订单列表

### 用户接口

- `POST /api/auth/login` - 用户登录
- `GET /api/user/profile` - 获取用户信息
- `GET /api/user/statistics` - 获取用户统计数据

## 部署说明

### 小程序发布

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台
4. 提交审核
5. 审核通过后发布

### 后端部署

- 确保后端 API 服务正常运行
- 配置 HTTPS 证书（小程序要求）
- 设置域名白名单

## 联系方式

- **公司名称**：济南小森国际贸易有限公司
- **客服电话**：18854121057
- **微信客服**：xiaosen2024
- **邮箱**：<EMAIL>
- **地址**：山东省济南市历城区荷花路街道付家村亿昌路 9 号

## 版本历史

### v1.0.0 (2024-08-08)

- 初始版本发布
- 实现核心功能模块
- 完成基础页面开发
- 集成价格计算器
- 添加用户系统

## 许可证

本项目仅供学习和参考使用。

---

**济南小森国际贸易有限公司** - 专业包装贸易服务
