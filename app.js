// 小森包装印刷厂小程序
App({
  onLaunch: function () {
    console.log("小森包装小程序启动");

    // 检查登录状态
    this.checkLoginStatus();

    // 获取系统信息
    this.getSystemInfo();
  },

  onShow: function (options) {
    console.log("小程序显示");
  },

  onHide: function () {
    console.log("小程序隐藏");
  },

  onError: function (msg) {
    console.error("小程序错误:", msg);
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync("token");
    if (token) {
      this.globalData.isLogin = true;
      this.globalData.token = token;
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res;
        console.log("系统信息:", res);
      },
    });
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLogin: false,
    token: "",
    systemInfo: {},
    baseUrl: "https://api.xiaosen.com", // API基础地址
    version: "1.0.0",
    pendingCategory: null,
  },
});
