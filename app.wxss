/* 小森包装小程序全局样式 */

/* 全局变量 */
page {
  --primary-color: #1e3a8a;
  --secondary-color: #f97316;
  --background-color: #f8fafc;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* 基础样式重置 */
view,
text,
image,
button,
input,
textarea,
scroll-view,
swiper,
swiper-item {
  box-sizing: border-box;
}

page {
  background-color: var(--background-color);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
}

/* 通用容器 */
.container {
  padding: 0 32rpx;
}

.page-container {
  min-height: 100vh;
  background-color: var(--background-color);
}

/* 卡片样式 */
.card {
  background-color: var(--white);
  border-radius: 16rpx;
  box-shadow: var(--shadow);
  margin-bottom: 24rpx;
  overflow: hidden;
}

.card-header {
  padding: 32rpx;
  border-bottom: 1px solid var(--border-color);
}

.card-body {
  padding: 32rpx;
}

.card-footer {
  padding: 32rpx;
  border-top: 1px solid var(--border-color);
  background-color: var(--gray-50);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: #1e40af;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--white);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 32rpx 64rpx;
  font-size: 32rpx;
}

.btn-block {
  width: 100%;
}

.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 文本样式 */
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 12rpx;
}

.caption {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.mt-8 {
  margin-top: 16rpx;
}
.mt-12 {
  margin-top: 24rpx;
}
.mt-16 {
  margin-top: 32rpx;
}
.mt-20 {
  margin-top: 40rpx;
}
.mt-24 {
  margin-top: 48rpx;
}

.mb-8 {
  margin-bottom: 16rpx;
}
.mb-12 {
  margin-bottom: 24rpx;
}
.mb-16 {
  margin-bottom: 32rpx;
}
.mb-20 {
  margin-bottom: 40rpx;
}
.mb-24 {
  margin-bottom: 48rpx;
}

.ml-8 {
  margin-left: 16rpx;
}
.ml-12 {
  margin-left: 24rpx;
}
.ml-16 {
  margin-left: 32rpx;
}

.mr-8 {
  margin-right: 16rpx;
}
.mr-12 {
  margin-right: 24rpx;
}
.mr-16 {
  margin-right: 32rpx;
}

.p-8 {
  padding: 16rpx;
}
.p-12 {
  padding: 24rpx;
}
.p-16 {
  padding: 32rpx;
}
.p-20 {
  padding: 40rpx;
}
.p-24 {
  padding: 48rpx;
}

/* 表单样式 */
.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: var(--white);
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* 加载和空状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx;
  color: var(--text-secondary);
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  color: var(--text-secondary);
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 32rpx 0;
}

/* 徽章 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  background-color: var(--primary-color);
  color: var(--white);
  min-width: 40rpx;
  height: 40rpx;
}

.badge-success {
  background-color: var(--success-color);
}

.badge-warning {
  background-color: var(--warning-color);
}

.badge-error {
  background-color: var(--error-color);
}
