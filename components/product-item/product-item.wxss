.product-item {
  flex-shrink: 0;
  width: 240rpx;
  background-color: var(--white);
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease;
}

.product-item:active {
  transform: scale(0.95);
}

.product-image {
  width: 100%;
  height: 180rpx;
}

.product-info {
  padding: 24rpx;
}

.product-name {
  display: block;
  font-size: 26rpx;
  color: var(--text-primary);
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--secondary-color);
}